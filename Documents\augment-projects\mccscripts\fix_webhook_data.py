#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to directly modify the webhook data file to fix the Bamboozle V issue
"""

import os
import re

# Read the webhook data file
webhook_data_file = "discord_webhook_data.txt"
if os.path.exists(webhook_data_file):
    with open(webhook_data_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # Find and modify the line with Bamboozle
    for i in range(len(lines)):
        if "Bamboozle" in lines[i]:
            # Replace Bamboozle I with Bamboozle V
            lines[i] = lines[i].replace("Bamboozle rIr", "Bamboozle rVr")
    
    # Write the modified data back to the file
    with open(webhook_data_file, 'w', encoding='utf-8') as file:
        file.writelines(lines)
    
    print(f"Modified {webhook_data_file} to fix Bamboozle V issue")
else:
    print(f"Webhook data file not found: {webhook_data_file}")
