import requests
import json
import random

# Read the webhook data from the file
webhook_data = {}
with open("login_webhook_data.txt", "r") as file:
    for line in file:
        if "=" in line:
            key, value = line.strip().split("=", 1)
            webhook_data[key] = value

# Get the webhook URL and message
webhook_url = webhook_data.get("WEBHOOK_URL", "")
message = webhook_data.get("MESSAGE", "")
no_embed = webhook_data.get("NO_EMBED", "false").lower() == "true"

# Create the payload
if no_embed:
    # Send only the message content without any embed
    payload = {
        "content": message
    }
else:
    # Create the embed (legacy support)
    color = int(webhook_data.get("COLOR", "65280"))  # Default to green
    no_gif = webhook_data.get("NO_GIF", "false").lower() == "true"

    embed = {
        "title": "Player Login",
        "description": message.split("**", 1)[1] if "**" in message else message,
        "color": color
    }

    # Only add image if NO_GIF is not set to true
    if not no_gif:
        # Random GIFs for login messages
        login_gifs = [
            "https://media.giphy.com/media/Nx0rz3jtxtEre/giphy.gif",
            "https://media.giphy.com/media/l0MYC0LajbaPoEADu/giphy.gif",
            "https://media.giphy.com/media/ASd0Ukj0y3qMM/giphy.gif",
            "https://media.giphy.com/media/3o7TKUAOqDm3SQle92/giphy.gif",
            "https://media.giphy.com/media/l4JyOCNEfXvVYEqB2/giphy.gif"
        ]
        embed["image"] = {
            "url": random.choice(login_gifs)
        }

    payload = {
        "content": message.split("**")[0].strip(),  # Extract the ping part
        "embeds": [embed]
    }

# Send the webhook
try:
    response = requests.post(
        webhook_url,
        data=json.dumps(payload),
        headers={"Content-Type": "application/json"}
    )
    response.raise_for_status()
    print(f"Webhook sent successfully! Status code: {response.status_code}")
except Exception as e:
    print(f"Error sending webhook: {e}")