//MCCScript 1.0
// Test script for Area Monitor - validates area boundaries and basic functionality

using System;
using System.Collections.Generic;

MCC.LoadBot(new TestAreaMonitor());

//MCCScript Extensions

public class TestAreaMonitor : ChatBot
{
    // Same area definition as the main monitor
    private readonly Location areaMin = new Location(-18, 83, -113);
    private readonly Location areaMax = new Location(18, 108, -78);

    public override void Initialize()
    {
        LogToConsole("=== Area Monitor Test Script ===");
        
        // Check required settings
        if (!GetEntityHandlingEnabled())
        {
            LogToConsole("ERROR: Entity handling is disabled!");
            LogToConsole("Enable 'entityhandling=true' in MinecraftClient.ini");
            UnloadBot();
            return;
        }

        LogToConsole("✓ Entity handling is enabled");
        
        if (GetTerrainEnabled())
        {
            LogToConsole("✓ Terrain handling is enabled");
        }
        else
        {
            LogToConsole("⚠ Terrain handling is disabled (recommended to enable)");
        }

        // Test area boundaries
        TestAreaBoundaries();
        
        // Test entity detection
        TestEntityDetection();
        
        // Register test commands
        RegisterChatBotCommand("testarea", "Test area monitoring functions", "testarea [boundaries|entities|position]", OnTestCommand);
        
        LogToConsole("=== Test Script Ready ===");
        LogToConsole("Use '/testarea boundaries' to test area boundary checking");
        LogToConsole("Use '/testarea entities' to list current entities");
        LogToConsole("Use '/testarea position' to check your current position");
    }

    private void TestAreaBoundaries()
    {
        LogToConsole("=== Testing Area Boundaries ===");
        LogToConsole($"Monitored area: X({areaMin.X} to {areaMax.X}), Y({areaMin.Y} to {areaMax.Y}), Z({areaMin.Z} to {areaMax.Z})");
        
        // Test corner points
        Location[] testPoints = {
            new Location(-18, 83, -113), // Bottom corner 1
            new Location(18, 83, -113),  // Bottom corner 2
            new Location(-18, 83, -78),  // Bottom corner 3
            new Location(18, 83, -78),   // Bottom corner 4
            new Location(-18, 108, -113), // Top corner 1
            new Location(18, 108, -113),  // Top corner 2
            new Location(-18, 108, -78),  // Top corner 3
            new Location(18, 108, -78),   // Top corner 4
            new Location(0, 95, -95),     // Center point
            new Location(-20, 95, -95),   // Outside X (too low)
            new Location(20, 95, -95),    // Outside X (too high)
            new Location(0, 80, -95),     // Outside Y (too low)
            new Location(0, 110, -95),    // Outside Y (too high)
            new Location(0, 95, -75),     // Outside Z (too high)
            new Location(0, 95, -115)     // Outside Z (too low)
        };

        for (int i = 0; i < testPoints.Length; i++)
        {
            Location point = testPoints[i];
            bool inArea = IsLocationInArea(point);
            string status = inArea ? "✓ INSIDE" : "✗ OUTSIDE";
            LogToConsole($"Point {i + 1}: ({point.X}, {point.Y}, {point.Z}) - {status}");
        }
    }

    private void TestEntityDetection()
    {
        LogToConsole("=== Testing Entity Detection ===");
        
        try
        {
            Dictionary<int, Entity> entities = GetEntities();
            if (entities != null)
            {
                LogToConsole($"Successfully retrieved {entities.Count} entities");
                
                int playerCount = 0;
                foreach (var entity in entities.Values)
                {
                    if (entity?.Type == EntityType.Player)
                    {
                        playerCount++;
                        bool inArea = IsLocationInArea(entity.Location);
                        string areaStatus = inArea ? "IN MONITORED AREA" : "outside area";
                        LogToConsole($"Player: {entity.Name} at ({entity.Location.X:F1}, {entity.Location.Y:F1}, {entity.Location.Z:F1}) - {areaStatus}");
                    }
                }
                
                LogToConsole($"Found {playerCount} players total");
            }
            else
            {
                LogToConsole("⚠ Could not retrieve entities (returned null)");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"✗ Error testing entity detection: {ex.Message}");
        }
    }

    private bool IsLocationInArea(Location loc)
    {
        return loc.X >= areaMin.X && loc.X <= areaMax.X &&
               loc.Y >= areaMin.Y && loc.Y <= areaMax.Y &&
               loc.Z >= areaMin.Z && loc.Z <= areaMax.Z;
    }

    public string OnTestCommand(string cmd, string[] args)
    {
        if (args.Length == 0)
        {
            return "Usage: /testarea [boundaries|entities|position|current]";
        }

        switch (args[0].ToLower())
        {
            case "boundaries":
                TestAreaBoundaries();
                return "Area boundary test completed - check console output";

            case "entities":
                TestEntityDetection();
                return "Entity detection test completed - check console output";

            case "position":
                try
                {
                    Location currentPos = GetCurrentLocation();
                    bool inArea = IsLocationInArea(currentPos);
                    string status = inArea ? "INSIDE monitored area" : "OUTSIDE monitored area";
                    LogToConsole($"Your position: ({currentPos.X:F2}, {currentPos.Y:F2}, {currentPos.Z:F2}) - {status}");
                    return $"Position: ({currentPos.X:F2}, {currentPos.Y:F2}, {currentPos.Z:F2}) - {status}";
                }
                catch (Exception ex)
                {
                    return $"Error getting position: {ex.Message}";
                }

            case "current":
                LogToConsole("=== Current Status ===");
                LogToConsole($"Entity handling: {(GetEntityHandlingEnabled() ? "Enabled" : "Disabled")}");
                LogToConsole($"Terrain handling: {(GetTerrainEnabled() ? "Enabled" : "Disabled")}");
                LogToConsole($"Inventory handling: {(GetInventoryEnabled() ? "Enabled" : "Disabled")}");
                
                try
                {
                    var entities = GetEntities();
                    LogToConsole($"Total entities: {entities?.Count ?? 0}");
                    
                    var onlinePlayers = GetOnlinePlayers();
                    LogToConsole($"Online players: {onlinePlayers?.Count ?? 0}");
                    
                    Location pos = GetCurrentLocation();
                    LogToConsole($"Your position: ({pos.X:F2}, {pos.Y:F2}, {pos.Z:F2})");
                }
                catch (Exception ex)
                {
                    LogToConsole($"Error getting status: {ex.Message}");
                }
                
                return "Current status logged to console";

            default:
                return "Unknown test option. Use: boundaries, entities, position, or current";
        }
    }

    public override void Update()
    {
        // This test script doesn't need continuous updates
        // The main area monitor script handles that
    }
}
