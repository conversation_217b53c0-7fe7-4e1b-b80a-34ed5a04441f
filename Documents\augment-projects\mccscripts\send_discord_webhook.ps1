# Script to send Discord webhook data from the file created by login_tracker.cs
# Place this script in the same directory as your MCC client

# Discord webhook URL
$webhookUrl = "https://discord.com/api/webhooks/1373055433538801714/G4HS8NED9mUYscI2LEJ4FJ-Ahs1pQuikIwi_x9p1XAxD2TIVkGQ32yqa5CDeJ-axqEWH"

# Path to the webhook data file
$webhookDataFile = "discord_webhook_data.txt"

# Check if the webhook data file exists
if (Test-Path $webhookDataFile) {
    Write-Host "Found webhook data file: $webhookDataFile" -ForegroundColor Cyan

    # Read the webhook data file
    $webhookData = Get-Content $webhookDataFile -Raw

    # Send the webhook
    try {
        Invoke-RestMethod -Uri $webhookUrl -Method Post -Body $webhookData -ContentType "application/json"
        Write-Host "Discord webhook sent successfully!" -ForegroundColor Green

        # Rename the file to prevent sending it again
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        Rename-Item -Path $webhookDataFile -NewName "discord_webhook_data_$timestamp.txt" -Force
        Write-Host "Webhook data file renamed to prevent resending." -ForegroundColor Cyan
    }
    catch {
        Write-Host "Error sending Discord webhook: $_" -ForegroundColor Red
    }
}
else {
    Write-Host "Webhook data file not found: $webhookDataFile" -ForegroundColor Yellow
    Write-Host "This file is created when a player joins or leaves the server." -ForegroundColor Yellow
}

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
