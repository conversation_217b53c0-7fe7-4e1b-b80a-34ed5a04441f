# PowerShell script to send Discord webhook
# This script reads webhook data from discord_webhook_data.txt and sends it to Discord

# Read webhook data
$webhookDataFile = "discord_webhook_data.txt"
if (-not (Test-Path $webhookDataFile)) {
    Write-Host "Webhook data file not found: $webhookDataFile" -ForegroundColor Yellow
    exit
}

$webhookData = Get-Content $webhookDataFile
$webhookUrl = ($webhookData | Where-Object { $_ -like 'WEBHOOK_URL=*' }).Substring('WEBHOOK_URL='.Length)
$playerName = ($webhookData | Where-Object { $_ -like 'PLAYER_NAME=*' }).Substring('PLAYER_NAME='.Length)
$location = ($webhookData | Where-Object { $_ -like 'LOCATION=*' }).Substring('LOCATION='.Length)

# Get equipment (may span multiple lines)
$equipmentLines = $webhookData | Where-Object { $_ -like 'EQUIPMENT=*' -or ($_ -notlike '*=*' -and $_ -notlike '#*') }
$equipmentText = ($equipmentLines[0] -replace 'EQUIPMENT=', '')
for ($i = 1; $i -lt $equipmentLines.Count; $i++) {
    $equipmentText += "`n" + $equipmentLines[$i]
}

# Clean the equipment text - remove all non-ASCII characters
$cleanEquipment = $equipmentText -replace '[^\x20-\x7E]', ''
$cleanEquipment = $cleanEquipment -replace '"', ''
$cleanEquipment = $cleanEquipment -replace "'", ""
$cleanEquipment = $cleanEquipment -replace "`n", "\\n"
$cleanEquipment = $cleanEquipment -replace "`r", ""

# Create a simple message
$message = "Player $playerName has entered the box area at $location\\n\\n$cleanEquipment"
$message = $message -replace '"', ''
$message = $message -replace "'", ""

Write-Host "Sending message:"
Write-Host $message

# Create a simple JSON payload
$json = '{"content":"' + $message + '"}'

Write-Host "JSON payload:"
Write-Host $json

# Send the webhook
try {
    $response = Invoke-RestMethod -Uri $webhookUrl -Method Post -Body $json -ContentType 'application/json'
    Write-Host "Discord webhook sent successfully!" -ForegroundColor Green
}
catch {
    Write-Host "Error sending Discord webhook: $_" -ForegroundColor Red
}
