# Box Area Monitor Configuration
# Edit this file to configure the script

# Discord webhook URL (required for Discord notifications)
DiscordWebhookUrl=https://discord.com/api/webhooks/1373110229386006638/uyy6NXtHVkjVbUKuMZPtht4Bhiv6MT-fbfzx78Qn_M7cKGr3OLMAdO2ykP41bsp8w3oT

# Enable or disable Discord notifications (true/false)
SendToDiscord=true

# Enable or disable sound alerts (true/false)
EnableSoundAlerts=true

# Enable or disable logging to file (true/false)
LogToFile=true

# Detection cooldown in seconds
DetectionCooldown=10
