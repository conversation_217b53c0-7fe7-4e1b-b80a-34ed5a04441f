//MCCScript 1.0

MCC.LoadBot(new LoginTracker());

//MCCScript Extensions

public class LoginTracker : ChatBot
{
    // Discord webhook URL
    private readonly string discordWebhookUrl = "https://discord.com/api/webhooks/1373110229386006638/uyy6NXtHVkjVbUKuMZPtht4Bhiv6MT-fbfzx78Qn_M7cKGr3OLMAdO2ykP41bsp8w3oT";

    // The specific player to track
    private readonly string targetPlayer = "ArcadeOwner";

    // Track if the target player is online
    private bool isTargetPlayerOnline = false;

    // Track the last time we checked for players
    private DateTime lastCheckTime = DateTime.MinValue;

    // Check interval in seconds (set to 1 second for near-instant detection)
    private readonly int checkInterval = 1;

    // Initialize the bot
    public override void Initialize()
    {
        LogToConsole("Login Tracker initialized!");

        // Check if the target player is online
        CheckTargetPlayerStatus();

        // Log a message to confirm the script is working
        LogToConsole($"Login Tracker is now monitoring player: {targetPlayer}");
        LogToConsole($"This script will check for player status every {checkInterval} seconds.");
    }

    // Update method that runs every second
    public override void Update()
    {
        // Check if it's time to update the player status
        if ((DateTime.Now - lastCheckTime).TotalSeconds >= checkInterval)
        {
            // Check if the target player's status has changed
            CheckTargetPlayerStatus();

            // Update the last check time
            lastCheckTime = DateTime.Now;
        }
    }

    // Check if the target player's status has changed
    private void CheckTargetPlayerStatus()
    {
        // Get the current list of online players
        string[] currentPlayers = GetOnlinePlayers();

        // Check if the target player is in the list
        bool isCurrentlyOnline = Array.Exists(currentPlayers, player => player.Equals(targetPlayer, StringComparison.OrdinalIgnoreCase));

        // If the status has changed
        if (isCurrentlyOnline != isTargetPlayerOnline)
        {
            if (isCurrentlyOnline)
            {
                // Player has joined
                LogToConsole($"Target player {targetPlayer} has joined the server.");

                // Send Discord notification
                SendLoginNotification(targetPlayer);
            }
            else
            {
                // Player has left
                LogToConsole($"Target player {targetPlayer} has left the server.");

                // Send Discord notification
                SendLogoutNotification(targetPlayer);
            }

            // Update the status
            isTargetPlayerOnline = isCurrentlyOnline;

            // Log the current status
            LogTargetPlayerStatus();
        }
    }

    // Log the target player's status
    private void LogTargetPlayerStatus()
    {
        if (isTargetPlayerOnline)
        {
            LogToConsole($"Target player {targetPlayer} is currently ONLINE.");
        }
        else
        {
            LogToConsole($"Target player {targetPlayer} is currently OFFLINE.");
        }
    }

    // We're not using the OnPlayerJoin and OnPlayerLeave events because they can cause NullReferenceException errors
    // Instead, we're using the polling approach with GetOnlinePlayers()

    // Check if a string is a valid player name
    private bool IsValidPlayerName(string name)
    {
        // Player names in Minecraft are 3-16 characters and only contain letters, numbers, and underscores
        if (string.IsNullOrEmpty(name) || name.Length < 3 || name.Length > 16)
            return false;

        // Check if the name contains only valid characters
        foreach (char c in name)
        {
            if (!char.IsLetterOrDigit(c) && c != '_')
                return false;
        }

        return true;
    }

    // Send login notification to Discord
    private void SendLoginNotification(string playerName)
    {
        // User IDs to ping
        string[] userIdsToPing = new string[] {
            "960647165766688779",
            "833561863476084767",
            "542140310524788736",
            "572991971359195138",
            "965501524304343082",
            "408828058552893460"
        };

        // Create a temporary file with the webhook data
        string webhookDataFile = "login_webhook_data.txt";

        // Format the message with pings for all users
        string pings = "";
        foreach (string id in userIdsToPing)
        {
            pings += $"<@{id}> ";
        }
        string message = $"{playerName} has joined the server {pings}";

        // Send the message 5 times with 0.5 second delay
        for (int i = 0; i < 5; i++)
        {
            // Create the webhook data
            List<string> webhookData = new List<string>();
            webhookData.Add($"WEBHOOK_URL={discordWebhookUrl}");
            webhookData.Add($"MESSAGE={message}");
            webhookData.Add("NO_EMBED=true"); // Don't include an embed

            // Write the webhook data to a file
            File.WriteAllLines(webhookDataFile, webhookData);

            // Execute the Python script
            System.Diagnostics.Process process = new System.Diagnostics.Process();
            process.StartInfo.FileName = "python";
            process.StartInfo.Arguments = "send_login_webhook.py";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.CreateNoWindow = true;
            process.Start();

            LogToConsole($"Sending Discord webhook #{i + 1} for target player login: {playerName}");

            // Wait 0.5 seconds before sending the next message (except for the last one)
            if (i < 4)
            {
                System.Threading.Thread.Sleep(500);
            }
        }
    }

    // Send logout notification to Discord
    private void SendLogoutNotification(string playerName)
    {
        // Create a temporary file with the webhook data
        string webhookDataFile = "logout_webhook_data.txt";

        // Format the message
        string message = $"**{playerName}** has left the server";

        // Create the webhook data
        List<string> webhookData = new List<string>();
        webhookData.Add($"WEBHOOK_URL={discordWebhookUrl}");
        webhookData.Add($"MESSAGE={message}");
        webhookData.Add("COLOR=16711680"); // Red color
        webhookData.Add("NO_GIF=true"); // Don't include a GIF

        // Write the webhook data to a file
        File.WriteAllLines(webhookDataFile, webhookData);

        // Execute the Python script
        System.Diagnostics.Process process = new System.Diagnostics.Process();
        process.StartInfo.FileName = "python";
        process.StartInfo.Arguments = "send_logout_webhook.py";
        process.StartInfo.UseShellExecute = false;
        process.StartInfo.CreateNoWindow = true;
        process.Start();

        LogToConsole($"Sending Discord webhook for target player logout: {playerName}");
    }
}