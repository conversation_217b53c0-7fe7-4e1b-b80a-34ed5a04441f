// MCC Script: Test formatting equipment for Discord
// This script tests the FormatEquipmentForDiscord method

using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Newtonsoft.Json;
using MinecraftClient.Inventory;
using MinecraftClient.Mapping;

// Script entry point
MCC.LoadBot(new TestFormat());

class TestFormat : ChatBot
{
    public override void Initialize()
    {
        LogToConsole("Testing equipment formatting...");
        
        // Create a test item
        Item testHelmet = new Item(ItemType.NetheriteHelmet, 1);
        testHelmet.DisplayName = "Test Helmet";
        testHelmet.Lores = new string[] {
            "§6Test Enchantment I",
            "§6Another Enchantment V",
            "§6Third Enchantment X",
            "§6Test Crate Exclusive"
        };
        
        // Create armor info
        ArmorInfo armor = new ArmorInfo();
        armor.HelmetItem = testHelmet;
        
        // Format the equipment
        string formatted = FormatEquipmentForDiscord(armor);
        
        // Log the formatted equipment
        LogToConsole("Formatted equipment:");
        LogToConsole(formatted);
        
        // Write to a test file
        File.WriteAllText("test_formatted_equipment.txt", formatted);
        LogToConsole("Wrote formatted equipment to test_formatted_equipment.txt");
        
        // Create webhook data
        List<string> webhookData = new List<string>();
        webhookData.Add("WEBHOOK_URL=https://discord.com/api/webhooks/1373110229386006638/uyy6NXtHVkjVbUKuMZPtht4Bhiv6MT-fbfzx78Qn_M7cKGr3OLMAdO2ykP41bsp8w3oT");
        webhookData.Add("PLAYER_NAME=TestPlayer");
        webhookData.Add("FORMATTED_EQUIPMENT=" + formatted);
        
        // Write the webhook data to a file
        File.WriteAllLines("test_webhook_data.txt", webhookData);
        LogToConsole("Wrote webhook data to test_webhook_data.txt");
        
        // Run the Python script
        System.Diagnostics.Process process = new System.Diagnostics.Process();
        process.StartInfo.FileName = "python";
        process.StartInfo.Arguments = "send_webhook.py";
        process.StartInfo.UseShellExecute = false;
        process.StartInfo.CreateNoWindow = true;
        process.Start();
        
        LogToConsole("Sent test webhook");
    }
    
    private string FormatEquipmentForDiscord(ArmorInfo armor)
    {
        // Format equipment for Discord with proper formatting
        StringBuilder formattedEquipment = new StringBuilder();
        
        // Log the start of formatting
        LogToConsole("Formatting equipment for Discord...");
        
        // Format helmet
        if (armor.HelmetItem != null)
        {
            string itemName = armor.HelmetItem.DisplayName;
            if (string.IsNullOrEmpty(itemName))
                itemName = armor.HelmetItem.Type.ToString();
                
            formattedEquipment.AppendLine($"**Helmet**: {itemName}");
            
            if (armor.HelmetItem.Lores != null && armor.HelmetItem.Lores.Length > 0)
            {
                // Get first two enchantments
                if (armor.HelmetItem.Lores.Length > 0)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.HelmetItem.Lores[0])}");
                    
                if (armor.HelmetItem.Lores.Length > 1)
                    formattedEquipment.AppendLine($"> {CleanLoreLine(armor.HelmetItem.Lores[1])}");
                
                // Get last enchantment (if it's a Crate Exclusive or different from first two)
                if (armor.HelmetItem.Lores.Length > 2)
                {
                    string lastLore = null;
                    
                    // Look for Crate Exclusive
                    for (int i = 0; i < armor.HelmetItem.Lores.Length; i++)
                    {
                        if (armor.HelmetItem.Lores[i].Contains("Crate Exclusive"))
                        {
                            lastLore = armor.HelmetItem.Lores[i];
                            break;
                        }
                    }
                    
                    // If no Crate Exclusive found, use the last lore line
                    if (lastLore == null)
                        lastLore = armor.HelmetItem.Lores[armor.HelmetItem.Lores.Length - 1];
                    
                    // Only add if different from first two
                    if (armor.HelmetItem.Lores.Length <= 1 || 
                        (lastLore != armor.HelmetItem.Lores[0] && lastLore != armor.HelmetItem.Lores[1]))
                    {
                        formattedEquipment.AppendLine($"> {CleanLoreLine(lastLore)}");
                    }
                }
            }
            
            formattedEquipment.AppendLine();
        }
        
        return formattedEquipment.ToString();
    }
    
    private string CleanLoreLine(string loreLine)
    {
        // Remove Minecraft formatting codes
        string cleaned = System.Text.RegularExpressions.Regex.Replace(loreLine, "§[0-9a-fk-or]", "");
        
        // Remove special characters
        cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"[^\x20-\x7E]", "");
        
        return cleaned.Trim();
    }
}
