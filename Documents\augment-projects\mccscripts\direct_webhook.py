#!/usr/bin/env python3
"""
Script to directly send a webhook with formatted equipment data
"""

import requests
import json

# Webhook URL
webhook_url = "https://discord.com/api/webhooks/1373110229386006638/uyy6NXtHVkjVbUKuMZPtht4Bhiv6MT-fbfzx78Qn_M7cKGr3OLMAdO2ykP41bsp8w3oT"

# Player name
player_name = "TestPlayer"

# Formatted equipment data
formatted_equipment = """**Helmet**: Test Helmet
> Test Enchantment I
> Another Enchantment V
> Test Crate Exclusive

**Chestplate**: Test Chestplate
> Test Enchantment I
> Another Enchantment V
> Test Crate Exclusive

**Leggings**: Test Leggings
> Test Enchantment I
> Another Enchantment V
> Test Crate Exclusive

**Boots**: Test Boots
> Test Enchantment I
> Another Enchantment V
> Test Crate Exclusive

**Main Hand**: Test Sword
> Test Enchantment I
> Another Enchantment V
> Test Crate Exclusive

**Off Hand**: Test Shield
> Test Enchantment I
> Another Enchantment V
> Test Crate Exclusive
"""

# Create the embed
embed = {
    "title": f"⚠️ {player_name} HAS DROPPED INTO WARZONE ⚠️",
    "description": formatted_equipment,
    "color": 15158332  # Red color
}

# Create the payload
payload = {
    "embeds": [embed]
}

print("Sending webhook with payload:")
print(json.dumps(payload, indent=2))

# Send the webhook
try:
    response = requests.post(webhook_url, json=payload)
    if response.status_code == 204:
        print("Discord webhook sent successfully!")
    else:
        print(f"Error sending webhook: {response.status_code} - {response.text}")
except Exception as e:
    print(f"Exception sending webhook: {e}")
