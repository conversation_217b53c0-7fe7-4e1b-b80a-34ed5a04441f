#!/usr/bin/env python3
"""
Test script to create a sample webhook data file with Grinch Sword and run the send_webhook.py script
"""

import os
import subprocess

# Create a sample webhook data file
with open("discord_webhook_data.txt", "w") as f:
    f.write("WEBHOOK_URL=https://discord.com/api/webhooks/1373110229386006638/uyy6NXtHVkjVbUKuMZPtht4Bhiv6MT-fbfzx78Qn_M7cKGr3OLMAdO2ykP41bsp8w3oT\n")
    f.write("PLAYER_NAME=GrinchPlayer\n")
    f.write("TIMESTAMP=2025-05-16 19:39:03\n")
    f.write("LOCATION=X:-0.4, Y:163.3, Z:-111.9\n")
    f.write("EQUIPMENT=Player has equipment:\n")
    f.write("Helmet: rTrorxrirc rRrart rHrerlrmrert rrrff Gift Wrap rIrrff Cold Heart rIIIrrff Refurbish rIXrrff Iron Fist rIIrrff Blacksmith rXrrff Rest rVrrff Bane Of The Dark rXVIrrff Darkness's Favor rXVIrrff Dark Protection rXIVrrff Demon Ward rXIXrrff Darkness's Blessing rXVIIIrrff Insatiable Greed rXIXrrff Insatiable Knowledge rXVIIrrff Demon Breaker rXVIrrff Blast Protection rVrrff Fire Protection rVrrff Mending rIrrff Projectile Protection rVrrff Protection rVrrff Unbreaking rVrrff t Virus XX trrff Grinch Crate Exclusive rrrr\n")
    f.write("Main Hand: Ginch Sword Feeze Beath II Poweful Blows V Runic Obstuction V Alacity III Leaden II Slam V Shockwave V Hangy V Decapitation IX Well Fed V Fiewok Footsteps III Fire Aspect III Looting III Grinch Crate Exclusive\n")
    f.write("Off Hand: TotemOfUndying\n")

print("Created test webhook data file")

# Run the send_webhook.py script
try:
    result = subprocess.run(["python", "send_webhook.py"], capture_output=True, text=True)
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")
    print(result.stderr)
    print(f"\nExit code: {result.returncode}")
except Exception as e:
    print(f"Error running send_webhook.py: {e}")
