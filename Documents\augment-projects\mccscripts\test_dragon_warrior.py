#!/usr/bin/env python3
"""
Test script to create a sample webhook data file with The Dragon Warrior and run the send_webhook.py script
"""

import os
import subprocess

# Create a sample webhook data file
with open("discord_webhook_data.txt", "w") as f:
    f.write("WEBHOOK_URL=https://discord.com/api/webhooks/1373110229386006638/uyy6NXtHVkjVbUKuMZPtht4Bhiv6MT-fbfzx78Qn_M7cKGr3OLMAdO2ykP41bsp8w3oT\n")
    f.write("PLAYER_NAME=TheMethodUE\n")
    f.write("TIMESTAMP=2025-05-16 19:39:03\n")
    f.write("LOCATION=X:-0.4, Y:163.3, Z:-111.9\n")
    f.write("EQUIPMENT=Player has equipment:\n")
    f.write("Helmet: Jrorlrlryr Hrerlrmrert rrrff Festive Dexterity rIrrff Snowball Barrage rVIrrff Echolocation rIrrff Lifeforce rIIIrrff Refurbish rIXrrff Blacksmith rXrrff Greenhouse rVrrff Iron Lungs rIrrff Meal Planning rVrrff Stay Calm rIrrff Clarity rVrrff Tesla Coil rVrr77 Blast Protection rffVrr77 Fire Protection rffVrr77 Mending rffIrr77 Projectile Protection rffVrr77 Protection rffVrr77 Respiration rffIVrr77 Unbreaking rffVr77 r rJrorlrlryr rCrrrartrer rErxrcrlrursrirvrer rr\n")
    f.write("Chestplate: Trhre rDrrrargrorn rWrarrrrriror Kung Fu rIrrff Bamboozle rVrrff Blacksmith rXrrff Iron Fist rIIrrff Refurbish rXrrff Fire Forged rIrrff Rest rVrr77 Blast Protection rffVrr77 Fire Protection rffVrr77 Mending rffIrr77 Projectile Protection rffVrr77 Protection rffVrr77 Unbreaking rffVr\n")
    f.write("Leggings: Irnrfrerrrnror Lrergrgrirnrgrsrr rrrff Revel In Anguish rIrrff Sell Soul rIIIrrff Refurbish rIVrrff Blacksmith rVIrrff Parry rIIIrr77 Blast Protection rffVrr77 Fire Protection rffVrr77 Mending rffIrr77 Projectile Protection rffVrr77 Protection rffVrr77 Unbreaking rffVrGlitch legsr77 rr rIrnrfrerrrnror rCrrrartrer rErxrcrlrursrirvrer rr\n")
    f.write("Off Hand: TotemOfUndying\n")

print("Created test webhook data file")

# Run the send_webhook.py script
try:
    result = subprocess.run(["python", "send_webhook.py"], capture_output=True, text=True)
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")
    print(result.stderr)
    print(f"\nExit code: {result.returncode}")
except Exception as e:
    print(f"Error running send_webhook.py: {e}")
