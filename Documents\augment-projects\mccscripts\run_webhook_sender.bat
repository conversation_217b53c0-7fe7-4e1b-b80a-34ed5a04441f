@echo off
echo Starting Discord Webhook Sender...

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Python found, running webhook sender...
    python send_discord_webhook.py --periodic 10
) else (
    where py >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo Python launcher found, running webhook sender...
        py send_discord_webhook.py --periodic 10
    ) else (
        echo Python not found. Please install Python to use this script.
        echo You can download Python from https://www.python.org/downloads/
        echo After installing Python, you may need to install the requests library:
        echo pip install requests
    )
)

pause
