//MCCScript 1.0
//using System;
//using System.Collections.Generic;
//using System.IO;
//using System.Text;
//using MinecraftClient;
//using MinecraftClient.Scripting;
//using MinecraftClient.Mapping;
//using MinecraftClient.Protocol.Handlers;

MCC.LoadBot(new LoginTracker());

//MCCScript Extensions

/// <summary>
/// This script tracks player logins and logouts and sends notifications to a Discord webhook.
/// </summary>
public class LoginTracker : ChatBot
{
    // Discord webhook URL
    private string webhookUrl = "https://discord.com/api/webhooks/1373055433538801714/G4HS8NED9mUYscI2LEJ4FJ-Ahs1pQuikIwi_x9p1XAxD2TIVkGQ32yqa5CDeJ-axqEWH";

    // Configuration options
    private bool logToFile = true;
    private string logFilePath = "login_tracker_log.txt";
    private string configFilePath = "login_tracker_config.json";

    // GIF URLs for join/leave messages
    private string[] joinGifUrls = new string[] {
        "https://media.giphy.com/media/Nx0rz3jtxtEre/giphy.gif",
        "https://media.giphy.com/media/l0MYC0LajbaPoEADu/giphy.gif",
        "https://media.giphy.com/media/ASd0Ukj0y3qMM/giphy.gif",
        "https://media.giphy.com/media/3o7TKUAOqDm3SQle92/giphy.gif",
        "https://media.giphy.com/media/l4JyOCNEfXvVYEqB2/giphy.gif"
    };

    private string[] leaveGifUrls = new string[] {
        "https://media.giphy.com/media/42D3CxaINsAFemFuId/giphy.gif",
        "https://media.giphy.com/media/3ohhwMULIutDIMNh28/giphy.gif",
        "https://media.giphy.com/media/l0HlBO7eyXzSZkJri/giphy.gif",
        "https://media.giphy.com/media/3o7TKEP6YngkCKFofC/giphy.gif",
        "https://media.giphy.com/media/26u4b45b8KlgAB7iM/giphy.gif"
    };

    // Random number generator for selecting GIFs
    private Random random = new Random();

    // Dictionary to track when players joined (to calculate session time)
    private Dictionary<string, DateTime> playerJoinTimes = new Dictionary<string, DateTime>();

    public override void Initialize()
    {
        // Load configuration
        LoadConfig();

        // Display initialization message
        LogToConsole("=== Login Tracker Initialized ===");
        LogToConsole($"Tracking player logins and logouts");
        LogToConsole($"Discord webhook: {(string.IsNullOrEmpty(webhookUrl) ? "Not set" : "Configured")}");

        // Initialize log file if enabled
        if (logToFile)
        {
            try
            {
                // Create or append to log file with header
                File.AppendAllText(logFilePath,
                    $"\n\n=== Login Tracker Session Started at {DateTime.Now} ===\n\n");
            }
            catch (Exception e)
            {
                LogToConsole($"Error creating log file: {e.Message}");
                logToFile = false;
            }
        }
    }

    private void LoadConfig()
    {
        try
        {
            // Check if config file exists
            if (File.Exists(configFilePath))
            {
                // Read the config file line by line
                string[] lines = File.ReadAllLines(configFilePath);

                foreach (string line in lines)
                {
                    // Skip comments and empty lines
                    if (string.IsNullOrWhiteSpace(line) || line.TrimStart().StartsWith("#"))
                        continue;

                    // Split by = to get key-value pairs
                    int equalsPos = line.IndexOf('=');
                    if (equalsPos > 0)
                    {
                        string key = line.Substring(0, equalsPos).Trim();
                        string value = line.Substring(equalsPos + 1).Trim();

                        // Apply settings based on key
                        switch (key.ToLower())
                        {
                            case "webhookurl":
                                webhookUrl = value;
                                break;

                            case "logtofile":
                                bool.TryParse(value, out logToFile);
                                break;
                        }
                    }
                }

                LogToConsole("Configuration loaded from file.");
            }
            else
            {
                // Create default config file
                List<string> configLines = new List<string>
                {
                    "# Login Tracker Configuration",
                    "# Edit this file to configure the script",
                    "",
                    "# Discord webhook URL",
                    $"WebhookURL={webhookUrl}",
                    "",
                    "# Enable or disable logging to file (true/false)",
                    $"LogToFile={logToFile}"
                };

                File.WriteAllLines(configFilePath, configLines);

                LogToConsole("Default configuration file created.");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error loading configuration: {ex.Message}");
            LogToConsole("Using default settings.");
        }
    }

    public override void OnPlayerJoin(Guid uuid, string name)
    {
        // Record join time for calculating session duration later
        playerJoinTimes[name] = DateTime.Now;

        // Log the event
        string message = $"Player {name} joined the server at {DateTime.Now}";
        LogToConsole(message);

        // Log to file
        if (logToFile)
        {
            try
            {
                File.AppendAllText(logFilePath, $"[{DateTime.Now}] JOIN: {name} (UUID: {uuid})\n");
            }
            catch (Exception ex)
            {
                LogToConsole($"Error writing to log file: {ex.Message}");
            }
        }

        // Send to Discord webhook
        SendJoinWebhook(name, uuid.ToString());
    }

    public override void OnPlayerLeave(Guid uuid, string name)
    {
        // Calculate session duration if we have the join time
        TimeSpan sessionDuration = TimeSpan.Zero;
        string durationText = "";

        if (playerJoinTimes.ContainsKey(name))
        {
            sessionDuration = DateTime.Now - playerJoinTimes[name];
            durationText = $" after {FormatTimeSpan(sessionDuration)}";
            playerJoinTimes.Remove(name);
        }

        // Log the event
        string message = $"Player {name} left the server at {DateTime.Now}{durationText}";
        LogToConsole(message);

        // Log to file
        if (logToFile)
        {
            try
            {
                File.AppendAllText(logFilePath,
                    $"[{DateTime.Now}] LEAVE: {name} (UUID: {uuid}){(sessionDuration > TimeSpan.Zero ? $", Session: {FormatTimeSpan(sessionDuration)}" : "")}\n");
            }
            catch (Exception ex)
            {
                LogToConsole($"Error writing to log file: {ex.Message}");
            }
        }

        // Send to Discord webhook
        SendLeaveWebhook(name, uuid.ToString(), sessionDuration);
    }

    private string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalHours >= 1)
        {
            return $"{(int)timeSpan.TotalHours}h {timeSpan.Minutes}m {timeSpan.Seconds}s";
        }
        else if (timeSpan.TotalMinutes >= 1)
        {
            return $"{(int)timeSpan.TotalMinutes}m {timeSpan.Seconds}s";
        }
        else
        {
            return $"{timeSpan.Seconds}s";
        }
    }

    private void SendJoinWebhook(string playerName, string uuid)
    {
        if (string.IsNullOrEmpty(webhookUrl))
        {
            LogToConsole("Discord webhook URL not set. Skipping webhook notification.");
            return;
        }

        try
        {
            // Get a random GIF URL
            string gifUrl = joinGifUrls[random.Next(joinGifUrls.Length)];

            // Create the webhook payload
            string payload = CreateWebhookPayload(
                "Player Joined",
                playerName,
                $"**{playerName}** has joined the server",
                0x00FF00, // Green color
                gifUrl,
                uuid);

            // Send the webhook
            SendWebhookRequest(webhookUrl, payload);

            LogToConsole($"Sent join notification to Discord for player {playerName}");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error sending Discord webhook: {ex.Message}");
        }
    }

    private void SendLeaveWebhook(string playerName, string uuid, TimeSpan sessionDuration)
    {
        if (string.IsNullOrEmpty(webhookUrl))
        {
            LogToConsole("Discord webhook URL not set. Skipping webhook notification.");
            return;
        }

        try
        {
            // Get a random GIF URL
            string gifUrl = leaveGifUrls[random.Next(leaveGifUrls.Length)];

            // Create the description with session duration if available
            string description = $"**{playerName}** has left the server";
            if (sessionDuration > TimeSpan.Zero)
            {
                description += $"\nSession duration: **{FormatTimeSpan(sessionDuration)}**";
            }

            // Create the webhook payload
            string payload = CreateWebhookPayload(
                "Player Left",
                playerName,
                description,
                0xFF0000, // Red color
                gifUrl,
                uuid);

            // Send the webhook
            SendWebhookRequest(webhookUrl, payload);

            LogToConsole($"Sent leave notification to Discord for player {playerName}");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error sending Discord webhook: {ex.Message}");
        }
    }

    private string CreateWebhookPayload(string title, string playerName, string description, int color, string imageUrl, string uuid)
    {
        // Get player avatar URL from Crafatar
        string avatarUrl = $"https://crafatar.com/avatars/{uuid}?size=128&overlay=true";

        // Format the timestamp
        string timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

        // Escape special characters for JSON
        title = EscapeJsonString(title);
        description = EscapeJsonString(description);

        // Create the JSON payload
        return "{"
            + "\"username\": \"Minecraft Login Tracker\","
            + "\"embeds\": ["
            + "  {"
            + $"    \"title\": \"{title}\","
            + $"    \"description\": \"{description}\","
            + $"    \"color\": {color},"
            + $"    \"timestamp\": \"{timestamp}\","
            + "    \"thumbnail\": {"
            + $"      \"url\": \"{avatarUrl}\""
            + "    },"
            + "    \"image\": {"
            + $"      \"url\": \"{imageUrl}\""
            + "    }"
            + "  }"
            + "]"
            + "}";
    }

    private string EscapeJsonString(string str)
    {
        if (string.IsNullOrEmpty(str))
            return string.Empty;

        return str
            .Replace("\\", "\\\\")
            .Replace("\"", "\\\"")
            .Replace("\n", "\\n")
            .Replace("\r", "\\r")
            .Replace("\t", "\\t");
    }

    private void SendWebhookRequest(string url, string jsonContent)
    {
        try
        {
            // Write the webhook data to a file for the Python script to process
            string webhookDataFile = "discord_webhook_data.txt";
            File.WriteAllText(webhookDataFile, jsonContent);

            LogToConsole($"Webhook data written to {webhookDataFile}");
            LogToConsole($"The Python script will automatically send this data to Discord");
            LogToConsole($"If the Python script is not running, start it with run_webhook_sender.bat");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error preparing webhook data: {ex.Message}");
        }
    }
}
