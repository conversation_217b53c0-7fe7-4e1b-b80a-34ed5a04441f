=== Minecraft Login Tracker ===

This system tracks player logins and logouts in Minecraft and sends notifications to a Discord webhook.

== Components ==

1. login_tracker.cs - The main script that monitors chat messages for player joins and leaves
2. send_login_webhook.py - Python script that sends login notifications to Discord
3. send_logout_webhook.py - Python script that sends logout notifications to Discord

== Setup ==

1. Make sure the login_tracker.cs script is enabled in MinecraftClient.ini:
   [ChatBot]
   [ChatBot.LoginTracker]
   Enabled = true

2. Install Python (if not already installed):
   - Download from https://www.python.org/downloads/
   - Make sure to check "Add Python to PATH" during installation

3. Install the required Python packages:
   - Open a command prompt and run:
     pip install requests

== Usage ==

1. Start the Minecraft Console Client with the script:
   - .\MinecraftClient.exe -s login_tracker.cs

2. The script will now:
   - Monitor chat messages for player joins and leaves
   - Track which players are online
   - Send Discord notifications when players join or leave

== How It Works ==

1. The login_tracker.cs script:
   - Monitors chat messages for patterns like "joined the game" or "left the game"
   - Extracts player names from these messages
   - Tracks which players are online in the online_players.txt file
   - Creates webhook data files when players join or leave
   - Calls the Python scripts to send the webhooks

2. The Python scripts:
   - Read the webhook data from the files
   - Create formatted Discord embeds with player information and random GIFs
   - Send the webhooks to Discord

== Customization ==

1. Discord Webhook URL:
   - The webhook URL is configured in login_tracker.cs
   - You can change it by editing the webhookUrl variable

2. GIF Images:
   - The script uses random GIFs for join/leave notifications
   - You can customize the GIF URLs in login_tracker.cs

3. Check Interval:
   - The Python script checks for webhook data every 10 seconds by default
   - You can change this by editing the interval in run_webhook_sender.bat

== Troubleshooting ==

1. If the webhook is not being sent:
   - Make sure Python is installed and in your PATH
   - Make sure the requests library is installed (pip install requests)
   - Check that the webhook URL is correct
   - Run test_webhook.py to test the webhook functionality

2. If the script is not detecting player joins/leaves:
   - Make sure the login_tracker.cs script is enabled in MinecraftClient.ini
   - Make sure the Minecraft Console Client is running with the correct server settings

== Files ==

- login_tracker.cs: The main script that monitors player joins and leaves
- online_players.txt: File that stores the list of online players between sessions
- login_webhook_data.txt: Temporary file with login webhook data
- logout_webhook_data.txt: Temporary file with logout webhook data
- send_login_webhook.py: Python script to send login notifications to Discord
- send_logout_webhook.py: Python script to send logout notifications to Discord

== Message Format Customization ==

If your server uses a different format for join/leave messages, you'll need to update the ExtractPlayerName method in login_tracker.cs. The current implementation handles formats like:

- "PlayerName joined the game"
- "[Rank] PlayerName joined the game"

You may need to adjust this based on your server's specific message format.
