# Run the Minecraft Console Client with the wz_simple.cs script
$scriptPath = Join-Path $PSScriptRoot "wz_simple.cs"
$exePath = Join-Path $PSScriptRoot "MinecraftClient.exe"

# Check if the script exists
if (-not (Test-Path $scriptPath)) {
    Write-Host "Error: Script file not found at $scriptPath" -ForegroundColor Red
    exit 1
}

# Check if the executable exists
if (-not (Test-Path $exePath)) {
    Write-Host "Error: MinecraftClient.exe not found at $exePath" -ForegroundColor Red
    exit 1
}

# Create the send_webhook.ps1 script if it doesn't exist
$webhookScriptPath = Join-Path $PSScriptRoot "send_webhook.ps1"
if (-not (Test-Path $webhookScriptPath)) {
    Write-Host "Creating send_webhook.ps1 script..." -ForegroundColor Yellow
    $webhookScript = @'
# PowerShell script to send Discord webhook
# This script reads webhook data from discord_webhook_data.txt and sends it to Discord

# Read webhook data
$webhookData = Get-Content 'discord_webhook_data.txt'
$webhookUrl = ($webhookData | Where-Object { $_ -like 'WEBHOOK_URL=*' }).Substring('WEBHOOK_URL='.Length)
$playerName = ($webhookData | Where-Object { $_ -like 'PLAYER_NAME=*' }).Substring('PLAYER_NAME='.Length)
$timestamp = ($webhookData | Where-Object { $_ -like 'TIMESTAMP=*' }).Substring('TIMESTAMP='.Length)
$location = ($webhookData | Where-Object { $_ -like 'LOCATION=*' }).Substring('LOCATION='.Length)

# Get equipment (may span multiple lines)
$equipmentLines = $webhookData | Where-Object { $_ -like 'EQUIPMENT=*' -or ($_ -notlike '*=*' -and $_ -notlike '#*') }
$equipment = ($equipmentLines[0] -replace 'EQUIPMENT=', '')
for ($i = 1; $i -lt $equipmentLines.Count; $i++) {
    $equipment += "`n" + $equipmentLines[$i]
}

# Create payload
$payload = @{
    embeds = @(
        @{
            title = "⚠️ $playerName has dropped into WZ ⚠️"
            description = "**Player detected in the box area at $timestamp**"
            color = 15158332
            fields = @(
                @{
                    name = "Equipment"
                    value = $equipment
                    inline = $false
                },
                @{
                    name = "📍 Location"
                    value = $location
                    inline = $true
                }
            )
            footer = @{
                text = "Box Area Monitor • $(Get-Date -Format 'yyyy-MM-dd')"
            }
            timestamp = "$(Get-Date -Format 'yyyy-MM-ddTHH:mm:ssZ')"
        }
    )
} | ConvertTo-Json -Depth 4

# Send webhook
try {
    Invoke-RestMethod -Uri $webhookUrl -Method Post -Body $payload -ContentType 'application/json'
    Write-Host "Discord webhook sent successfully!"
    
    # Rename the file to prevent resending
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    Rename-Item -Path "discord_webhook_data.txt" -NewName "discord_webhook_data_$timestamp.txt" -Force
} catch {
    Write-Host "Error sending Discord webhook: $_"
}
'@
    Set-Content -Path $webhookScriptPath -Value $webhookScript
    Write-Host "Created send_webhook.ps1 script" -ForegroundColor Green
}

# Run the Minecraft Console Client with the script
Write-Host "Running MinecraftClient with wz_simple.cs script..." -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the client" -ForegroundColor Yellow

# Start the client
& $exePath

# Start a background job to monitor for webhook data files
$job = Start-Job -ScriptBlock {
    $webhookDataPath = Join-Path $using:PSScriptRoot "discord_webhook_data.txt"
    $webhookScriptPath = Join-Path $using:PSScriptRoot "send_webhook.ps1"
    
    while ($true) {
        if (Test-Path $webhookDataPath) {
            Write-Host "Found webhook data file, sending to Discord..." -ForegroundColor Cyan
            & powershell.exe -ExecutionPolicy Bypass -File $webhookScriptPath
            Start-Sleep -Seconds 1
        }
        Start-Sleep -Seconds 2
    }
}

# Wait for the client to exit
try {
    Wait-Process -Id $process.Id
}
catch {
    # Client exited
}
finally {
    # Stop the background job
    Stop-Job -Job $job
    Remove-Job -Job $job
}
