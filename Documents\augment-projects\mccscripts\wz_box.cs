//MCCScript 1.0

// Box Area Monitor Script
// Monitors a box-shaped area for players and logs their equipment
// Version 3.0

MCC.LoadBot(new BoxAreaMonitor());

//MCCScript Extensions

using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Diagnostics;
using MinecraftClient.Scripting;
using MinecraftClient.Mapping;

class BoxAreaMonitor : ChatBot
{
    // Configuration
    private Location areaMin = new Location(-10, 100, -140);
    private Location areaMax = new Location(10, 200, -100);
    private int detectionCooldown = 10; // seconds
    private bool enableSoundAlerts = true;
    private bool logToFile = true;
    private string logFilePath = "box_monitor_log.txt";
    private bool sendToDiscord = true;
    private string discordWebhookUrl = "";
    private string configFilePath = "box_monitor_config.json";

    // State
    private Dictionary<string, PlayerInfo> playersInArea = new Dictionary<string, PlayerInfo>();
    private Dictionary<string, DateTime> playerCooldowns = new Dictionary<string, DateTime>();
    private DateTime lastScan = DateTime.MinValue;

    public override void Initialize()
    {
        LogToConsole("Box Area Monitor initialized");
        LogToConsole($"Monitoring area: ({areaMin.X}, {areaMin.Y}, {areaMin.Z}) to ({areaMax.X}, {areaMax.Y}, {areaMax.Z})");

        // Load configuration
        LoadConfig();

        // Register the timer callback
        SetupTimer();
    }

    private void LoadConfig()
    {
        try
        {
            if (File.Exists(configFilePath))
            {
                string json = File.ReadAllText(configFilePath);

                // Parse the JSON manually since we can't use JsonConvert
                if (json.Contains("\"areaMin\""))
                {
                    // Extract areaMin
                    int minStart = json.IndexOf("\"areaMin\"");
                    int minXStart = json.IndexOf("\"X\":", minStart);
                    int minYStart = json.IndexOf("\"Y\":", minStart);
                    int minZStart = json.IndexOf("\"Z\":", minStart);

                    if (minXStart > 0 && minYStart > 0 && minZStart > 0)
                    {
                        double x = double.Parse(GetJsonValue(json, minXStart + 4));
                        double y = double.Parse(GetJsonValue(json, minYStart + 4));
                        double z = double.Parse(GetJsonValue(json, minZStart + 4));
                        areaMin = new Location(x, y, z);
                    }
                }

                if (json.Contains("\"areaMax\""))
                {
                    // Extract areaMax
                    int maxStart = json.IndexOf("\"areaMax\"");
                    int maxXStart = json.IndexOf("\"X\":", maxStart);
                    int maxYStart = json.IndexOf("\"Y\":", maxStart);
                    int maxZStart = json.IndexOf("\"Z\":", maxStart);

                    if (maxXStart > 0 && maxYStart > 0 && maxZStart > 0)
                    {
                        double x = double.Parse(GetJsonValue(json, maxXStart + 4));
                        double y = double.Parse(GetJsonValue(json, maxYStart + 4));
                        double z = double.Parse(GetJsonValue(json, maxZStart + 4));
                        areaMax = new Location(x, y, z);
                    }
                }

                // Extract other settings
                if (json.Contains("\"detectionCooldown\""))
                {
                    int start = json.IndexOf("\"detectionCooldown\"");
                    detectionCooldown = int.Parse(GetJsonValue(json, start + 20));
                }

                if (json.Contains("\"enableSoundAlerts\""))
                {
                    int start = json.IndexOf("\"enableSoundAlerts\"");
                    string value = GetJsonValue(json, start + 20);
                    enableSoundAlerts = value.ToLower() == "true";
                }

                if (json.Contains("\"logToFile\""))
                {
                    int start = json.IndexOf("\"logToFile\"");
                    string value = GetJsonValue(json, start + 12);
                    logToFile = value.ToLower() == "true";
                }

                if (json.Contains("\"logFilePath\""))
                {
                    int start = json.IndexOf("\"logFilePath\"");
                    logFilePath = GetJsonStringValue(json, start + 14);
                }

                if (json.Contains("\"sendToDiscord\""))
                {
                    int start = json.IndexOf("\"sendToDiscord\"");
                    string value = GetJsonValue(json, start + 16);
                    sendToDiscord = value.ToLower() == "true";
                }

                if (json.Contains("\"discordWebhookUrl\""))
                {
                    int start = json.IndexOf("\"discordWebhookUrl\"");
                    discordWebhookUrl = GetJsonStringValue(json, start + 20);
                }

                LogToConsole("Configuration loaded from file");
            }
            else
            {
                // Create default configuration
                SaveConfig();
                LogToConsole("Default configuration created");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error loading configuration: {ex.Message}");
            // Create default configuration
            SaveConfig();
        }
    }

    private string GetJsonValue(string json, int startIndex)
    {
        int valueStart = json.IndexOf(':', startIndex) + 1;
        while (valueStart < json.Length && (json[valueStart] == ' ' || json[valueStart] == '\t' || json[valueStart] == '\r' || json[valueStart] == '\n'))
            valueStart++;

        int valueEnd = json.IndexOfAny(new char[] { ',', '}' }, valueStart);
        if (valueEnd < 0)
            valueEnd = json.Length;

        return json.Substring(valueStart, valueEnd - valueStart).Trim();
    }

    private string GetJsonStringValue(string json, int startIndex)
    {
        int valueStart = json.IndexOf('"', startIndex) + 1;
        int valueEnd = json.IndexOf('"', valueStart);
        return json.Substring(valueStart, valueEnd - valueStart);
    }

    private void SaveConfig()
    {
        try
        {
            // Create JSON manually
            StringBuilder json = new StringBuilder();
            json.AppendLine("{");
            json.AppendLine($"  \"areaMin\": {{ \"X\": {areaMin.X}, \"Y\": {areaMin.Y}, \"Z\": {areaMin.Z} }},");
            json.AppendLine($"  \"areaMax\": {{ \"X\": {areaMax.X}, \"Y\": {areaMax.Y}, \"Z\": {areaMax.Z} }},");
            json.AppendLine($"  \"detectionCooldown\": {detectionCooldown},");
            json.AppendLine($"  \"enableSoundAlerts\": {enableSoundAlerts.ToString().ToLower()},");
            json.AppendLine($"  \"logToFile\": {logToFile.ToString().ToLower()},");
            json.AppendLine($"  \"logFilePath\": \"{logFilePath}\",");
            json.AppendLine($"  \"sendToDiscord\": {sendToDiscord.ToString().ToLower()},");
            json.AppendLine($"  \"discordWebhookUrl\": \"{discordWebhookUrl}\"");
            json.AppendLine("}");

            File.WriteAllText(configFilePath, json.ToString());
            LogToConsole("Configuration saved to file");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error saving configuration: {ex.Message}");
        }
    }

    private void SetupTimer()
    {
        // Register a timer to scan for players every second
        RegisterChatBotTimer(ScanForPlayers, 1000);
    }

    private void ScanForPlayers(object state)
    {
        try
        {
            // Get the current world
            World world = GetWorld();
            if (world == null)
                return;

            // Check if enough time has passed since the last scan
            if ((DateTime.Now - lastScan).TotalSeconds < 1)
                return;

            lastScan = DateTime.Now;

            // Track which players are still in the area
            HashSet<string> currentPlayers = new HashSet<string>();

            // Get all entities
            Dictionary<int, Entity> entities = new Dictionary<int, Entity>();
            foreach (var entity in world.GetEntities())
            {
                if (entity != null)
                    entities[entity.ID] = entity;
            }

            // Check each entity
            foreach (var entity in entities.Values)
            {
                // Skip non-player entities
                if (entity.Type != EntityType.Player || entity.Name == GetUsername())
                    continue;

                // Check if the player is in the box area
                if (IsLocationInArea(entity.Location))
                {
                    currentPlayers.Add(entity.Name);

                    // Check if player is on cooldown
                    bool isOnCooldown = false;
                    if (playerCooldowns.ContainsKey(entity.Name))
                    {
                        TimeSpan timeSinceCooldown = DateTime.Now - playerCooldowns[entity.Name];
                        if (timeSinceCooldown.TotalSeconds < detectionCooldown)
                        {
                            isOnCooldown = true;
                        }
                        else
                        {
                            // Cooldown expired, remove from cooldown list
                            playerCooldowns.Remove(entity.Name);
                        }
                    }

                    // If this is a new player in the area and not on cooldown
                    if (!playersInArea.ContainsKey(entity.Name) && !isOnCooldown)
                    {
                        // Analyze armor
                        ArmorInfo armorInfo = AnalyzeArmor(entity);

                        // Create player info
                        PlayerInfo playerInfo = new PlayerInfo
                        {
                            EnteredTime = DateTime.Now,
                            LastPosition = entity.Location,
                            ArmorInfo = armorInfo,
                            EquipmentChecked = true
                        };

                        playersInArea[entity.Name] = playerInfo;
                        OnPlayerEnteredArea(entity, armorInfo);

                        // Add player to cooldown list
                        playerCooldowns[entity.Name] = DateTime.Now;
                    }
                    else if (playersInArea.ContainsKey(entity.Name))
                    {
                        // Update the player info without triggering alerts
                        playersInArea[entity.Name].LastPosition = entity.Location;
                    }
                }
            }

            // Check for players who have left the area
            List<string> playersToRemove = new List<string>();
            foreach (string playerName in playersInArea.Keys)
            {
                if (!currentPlayers.Contains(playerName))
                {
                    // Player has left the area
                    OnPlayerLeftArea(playerName, playersInArea[playerName]);
                    playersToRemove.Add(playerName);
                }
            }

            // Remove players who have left
            foreach (string playerName in playersToRemove)
            {
                playersInArea.Remove(playerName);
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in ScanForPlayers: {ex.Message}");
        }
    }

    private bool IsLocationInArea(Location location)
    {
        return location.X >= areaMin.X && location.X <= areaMax.X &&
               location.Y >= areaMin.Y && location.Y <= areaMax.Y &&
               location.Z >= areaMin.Z && location.Z <= areaMax.Z;
    }

    private ArmorInfo AnalyzeArmor(Entity entity)
    {
        ArmorInfo armor = new ArmorInfo();

        // Get equipment
        Dictionary<int, Item> equipment = new Dictionary<int, Item>();
        foreach (var slot in entity.GetEquipment())
        {
            equipment[slot.Key] = slot.Value;
        }

        // Log equipment data
        LogToConsole($"COMPLETE EQUIPMENT DATA FOR {entity.Name}:");
        LogToConsole($"Equipment dictionary contains {equipment.Count} slots");

        foreach (var slot in equipment)
        {
            Item item = slot.Value;
            if (item != null)
            {
                LogToConsole($"Slot {slot.Key}: Type={item.Type}, DisplayName={item.DisplayName}");

                // Log NBT data if available
                if (item.NBT != null && item.NBT.Count > 0)
                {
                    LogToConsole($"  NBT Data ({item.NBT.Count} entries):");
                    foreach (var nbt in item.NBT)
                    {
                        LogToConsole($"    {nbt.Key}: {nbt.Value}");
                    }
                }

                // Log lore if available
                if (item.Lores != null && item.Lores.Length > 0)
                {
                    LogToConsole($"  Lore ({item.Lores.Length} lines):");
                    for (int i = 0; i < item.Lores.Length; i++)
                    {
                        LogToConsole($"    Line {i + 1}: {item.Lores[i]}");
                    }
                }
            }
        }

        // Log slot summary
        LogToConsole("EQUIPMENT SLOT SUMMARY:");
        LogSlotInfo(equipment, 0, "Main Hand");
        LogSlotInfo(equipment, 1, "Boots");
        LogSlotInfo(equipment, 2, "Helmet");
        LogSlotInfo(equipment, 3, "Unknown");
        LogSlotInfo(equipment, 4, "Unknown");
        LogSlotInfo(equipment, 5, "Unknown");
        LogSlotInfo(equipment, 6, "Unknown");
        LogSlotInfo(equipment, 64, "Off Hand");
        LogSlotInfo(equipment, 65, "Leggings");
        LogSlotInfo(equipment, 66, "Chestplate");

        // Check each equipment slot
        if (equipment.ContainsKey(2) && equipment[2] != null) // Helmet
        {
            armor.Helmet = GetItemWithLore(equipment[2]);
        }

        if (equipment.ContainsKey(66) && equipment[66] != null) // Chestplate
        {
            armor.Chestplate = GetItemWithLore(equipment[66]);
        }

        if (equipment.ContainsKey(65) && equipment[65] != null) // Leggings
        {
            armor.Leggings = GetItemWithLore(equipment[65]);
        }

        if (equipment.ContainsKey(1) && equipment[1] != null) // Boots
        {
            armor.Boots = GetItemWithLore(equipment[1]);
        }

        if (equipment.ContainsKey(0) && equipment[0] != null) // Main hand
        {
            armor.MainHand = GetItemWithLore(equipment[0]);
        }

        if (equipment.ContainsKey(64) && equipment[64] != null) // Off hand
        {
            armor.OffHand = GetItemWithLore(equipment[64]);
        }

        // Log all equipment slots
        LogToConsole($"All equipment slots for {entity.Name}: {string.Join(", ", equipment.Keys)}");

        return armor;
    }

    private void LogSlotInfo(Dictionary<int, Item> equipment, int slot, string slotName)
    {
        if (equipment.ContainsKey(slot) && equipment[slot] != null)
        {
            Item item = equipment[slot];
            string displayName = !string.IsNullOrEmpty(item.DisplayName) ? item.DisplayName : item.Type.ToString();
            LogToConsole($"  Slot {slot} ({slotName}): {item.Type} / {displayName}");
        }
        else
        {
            LogToConsole($"  Slot {slot} ({slotName}): NOT PRESENT");
        }
    }

    private string GetItemWithLore(Item item)
    {
        if (item == null)
            return "None";

        string displayName = !string.IsNullOrEmpty(item.DisplayName) ? item.DisplayName : item.Type.ToString();

        // Add lore if available
        if (item.Lores != null && item.Lores.Length > 0)
        {
            string lorePreview = string.Join(", ", item.Lores.Length > 2 ?
                new string[] { item.Lores[0], item.Lores[1] + "..." } :
                item.Lores);
            return $"{displayName} ({lorePreview})";
        }

        return displayName;
    }

    private string GetItemWithFullLore(Item item)
    {
        if (item == null)
            return "None";

        string displayName = !string.IsNullOrEmpty(item.DisplayName) ? item.DisplayName : item.Type.ToString();

        // Add full lore
        if (item.Lores != null && item.Lores.Length > 0)
        {
            string result = displayName;
            foreach (string lore in item.Lores)
            {
                // Clean up the lore line
                string cleanLore = lore.Replace("§", "").Trim();
                if (!string.IsNullOrEmpty(cleanLore))
                {
                    result += "\n" + cleanLore;
                }
            }
            return result;
        }

        return displayName;
    }

    private void OnPlayerEnteredArea(Entity player, ArmorInfo armor)
    {
        // Log the detection
        LogToConsole($"ALERT: Player {player.Name} detected in the box area at {DateTime.Now}");
        LogToConsole($"Position: X:{player.Location.X:F1}, Y:{player.Location.Y:F1}, Z:{player.Location.Z:F1}");

        // Log equipment
        LogToConsole($"Player {player.Name} equipment:");
        if (armor.Helmet != "None")
            LogToConsole($"  Equipment: {armor.Helmet}");
        if (armor.Chestplate != "None")
            LogToConsole($"  Equipment: {armor.Chestplate}");
        if (armor.Leggings != "None")
            LogToConsole($"  Equipment: {armor.Leggings}");
        if (armor.Boots != "None" && !armor.Boots.Contains("Not visible"))
            LogToConsole($"  Equipment: {armor.Boots}");
        if (armor.MainHand != "None" && !armor.MainHand.Contains("Not visible"))
            LogToConsole($"  Equipment: {armor.MainHand}");
        if (armor.OffHand != "None")
            LogToConsole($"  Equipment: {armor.OffHand}");

        // Write to log file
        if (logToFile)
        {
            try
            {
                string logEntry = $"[{DateTime.Now}] Player {player.Name} detected in box area at X:{player.Location.X:F1}, Y:{player.Location.Y:F1}, Z:{player.Location.Z:F1}\n";
                logEntry += $"Equipment: {GetArmorSummary(armor)}\n";
                File.AppendAllText(logFilePath, logEntry);
            }
            catch (Exception ex)
            {
                LogToConsole($"Error writing to log file: {ex.Message}");
            }
        }

        // Send to Discord
        if (sendToDiscord)
        {
            SendToDiscord(player.Name, player.Location, armor);
        }

        // Play sound alert
        if (enableSoundAlerts)
        {
            Console.Beep(1000, 500);
        }
    }

    private void OnPlayerLeftArea(string playerName, PlayerInfo playerInfo)
    {
        // Calculate time spent in area
        TimeSpan timeInArea = DateTime.Now - playerInfo.EnteredTime;

        // Log the event
        LogToConsole($"Player {playerName} left the box area after {timeInArea.TotalMinutes:F1} minutes");

        // Write to log file
        if (logToFile)
        {
            try
            {
                string logEntry = $"[{DateTime.Now}] Player {playerName} left box area after {timeInArea.TotalMinutes:F1} minutes\n";
                File.AppendAllText(logFilePath, logEntry);
            }
            catch (Exception ex)
            {
                LogToConsole($"Error writing to log file: {ex.Message}");
            }
        }
    }

    private string GetArmorSummary(ArmorInfo armor)
    {
        List<string> equipment = new List<string>();

        if (armor.Helmet != "None")
            equipment.Add(armor.Helmet);

        if (armor.Chestplate != "None")
            equipment.Add(armor.Chestplate);

        if (armor.Leggings != "None")
            equipment.Add(armor.Leggings);

        if (armor.Boots != "None" && !armor.Boots.Contains("Not visible"))
            equipment.Add(armor.Boots);

        if (armor.MainHand != "None" && !armor.MainHand.Contains("Not visible"))
            equipment.Add(armor.MainHand);

        if (armor.OffHand != "None")
            equipment.Add(armor.OffHand);

        if (equipment.Count == 0)
            return "No equipment";

        return string.Join(", ", equipment);
    }

    private void SendToDiscord(string playerName, Location location, ArmorInfo armor)
    {
        if (!sendToDiscord || string.IsNullOrEmpty(discordWebhookUrl))
        {
            LogToConsole("Discord notifications are disabled or webhook URL is not set.");
            LogToConsole("Edit the configuration file to set your Discord webhook URL.");
            return;
        }

        try
        {
            // Create a webhook data file that can be processed by an external script
            string webhookDataFile = "discord_webhook_data.txt";

            // Build the equipment list
            List<string> equipmentList = new List<string>();

            if (armor.Helmet != "None")
                equipmentList.Add("🪖 " + armor.Helmet);

            if (armor.Chestplate != "None")
                equipmentList.Add("🛡️ " + armor.Chestplate);

            if (armor.Leggings != "None")
                equipmentList.Add("👖 " + armor.Leggings);

            if (armor.Boots != "None" && !armor.Boots.Contains("Not visible"))
                equipmentList.Add("👢 " + armor.Boots);

            if (armor.MainHand != "None" && !armor.MainHand.Contains("Not visible"))
                equipmentList.Add("⚔️ " + armor.MainHand);

            if (armor.OffHand != "None")
                equipmentList.Add("🧿 " + armor.OffHand);

            string equipmentText = equipmentList.Count > 0
                ? string.Join("\n", equipmentList)
                : "No equipment detected";

            // Create the webhook data
            List<string> webhookData = new List<string>
            {
                $"WEBHOOK_URL={discordWebhookUrl}",
                $"PLAYER_NAME={playerName}",
                $"TIMESTAMP={DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}",
                $"LOCATION=X:{location.X:F1}, Y:{location.Y:F1}, Z:{location.Z:F1}",
                $"EQUIPMENT={equipmentText}"
            };

            // Write the webhook data to a file
            File.WriteAllLines(webhookDataFile, webhookData);

            // Execute the PowerShell script
            Process process = new Process();
            process.StartInfo.FileName = "powershell.exe";
            process.StartInfo.Arguments = "-ExecutionPolicy Bypass -File send_webhook.ps1";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.CreateNoWindow = true;
            process.Start();

            LogToConsole($"Sending Discord webhook for player {playerName}...");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error sending Discord webhook: {ex.Message}");
        }
    }

    public override void GetText(string text)
    {
        // Process chat messages
        string message = GetVerbatim(text);

        // Log chat messages from players in the box area
        foreach (string playerName in playersInArea.Keys)
        {
            if (message.Contains($"<{playerName}>") || message.Contains($"[{playerName}]"))
            {
                LogToConsole($"Player in box area said: {message}");

                // Write to log file
                if (logToFile)
                {
                    try
                    {
                        string logEntry = $"[{DateTime.Now}] Chat: {message}\n";
                        File.AppendAllText(logFilePath, logEntry);
                    }
                    catch (Exception ex)
                    {
                        LogToConsole($"Error writing to log file: {ex.Message}");
                    }
                }

                break;
            }
        }

        // Process commands
        string command = "";
        string username = "";
        if (IsPrivateMessage(text, ref command, ref username) && username == GetUsername())
        {
            if (command.StartsWith("!box"))
            {
                string[] args = command.Split(' ');

                if (args.Length > 1)
                {
                    switch (args[1].ToLower())
                    {
                        case "list":
                            // List players in the box area
                            if (playersInArea.Count == 0)
                            {
                                SendPrivateMessage(username, "No players currently in the box area.");
                            }
                            else
                            {
                                SendPrivateMessage(username, $"{playersInArea.Count} players in box area:");
                                foreach (var player in playersInArea)
                                {
                                    TimeSpan timeInArea = DateTime.Now - player.Value.EnteredTime;
                                    SendPrivateMessage(username, $"- {player.Key} (for {timeInArea.TotalMinutes:F1} minutes)");
                                    SendPrivateMessage(username, $"  Armor: {GetArmorSummary(player.Value.ArmorInfo)}");
                                }
                            }
                            break;

                        case "cooldown":
                            // Set detection cooldown
                            if (args.Length > 2 && int.TryParse(args[2], out int newCooldown) && newCooldown >= 0)
                            {
                                detectionCooldown = newCooldown;
                                SendPrivateMessage(username, $"Detection cooldown set to {detectionCooldown} seconds.");
                                SaveConfig();
                            }
                            else
                            {
                                SendPrivateMessage(username, $"Current detection cooldown is {detectionCooldown} seconds.");
                                SendPrivateMessage(username, "Use '!box cooldown <seconds>' to change it.");
                            }
                            break;

                        case "sound":
                            // Toggle sound alerts
                            if (args.Length > 2)
                            {
                                if (args[2].ToLower() == "on")
                                {
                                    enableSoundAlerts = true;
                                    SendPrivateMessage(username, "Sound alerts enabled.");
                                    SaveConfig();
                                }
                                else if (args[2].ToLower() == "off")
                                {
                                    enableSoundAlerts = false;
                                    SendPrivateMessage(username, "Sound alerts disabled.");
                                    SaveConfig();
                                }
                            }
                            break;

                        case "config":
                            // Configuration commands
                            if (args.Length > 2)
                            {
                                if (args[2].ToLower() == "reload")
                                {
                                    LoadConfig();
                                    SendPrivateMessage(username, "Configuration reloaded from file.");
                                }
                                else if (args[2].ToLower() == "save")
                                {
                                    SaveConfig();
                                    SendPrivateMessage(username, "Configuration saved to file.");
                                }
                            }
                            else
                            {
                                // Show current configuration
                                SendPrivateMessage(username, $"Discord notifications: {(sendToDiscord ? "Enabled" : "Disabled")}");
                                SendPrivateMessage(username, $"Sound alerts: {(enableSoundAlerts ? "Enabled" : "Disabled")}");
                                SendPrivateMessage(username, $"Log to file: {(logToFile ? "Enabled" : "Disabled")}");
                                SendPrivateMessage(username, $"Detection cooldown: {detectionCooldown} seconds");
                                SendPrivateMessage(username, "Edit the config file to change these settings.");
                                SendPrivateMessage(username, "Use '!box config reload' to reload the configuration.");
                            }
                            break;

                        case "help":
                            // Show help
                            SendPrivateMessage(username, "Box Area Monitor Commands:");
                            SendPrivateMessage(username, "!box list - List players in box area");
                            SendPrivateMessage(username, "!box cooldown <seconds> - Set detection cooldown");
                            SendPrivateMessage(username, "!box sound on/off - Toggle sound alerts");
                            SendPrivateMessage(username, "!box config - Show current configuration");
                            SendPrivateMessage(username, "!box config reload - Reload configuration from file");
                            SendPrivateMessage(username, "!box config save - Save current settings to file");
                            SendPrivateMessage(username, "!box help - Show this help");
                            break;
                    }
                }
                else
                {
                    // Default command
                    SendPrivateMessage(username, $"Monitoring box area with {playersInArea.Count} players inside.");
                    SendPrivateMessage(username, $"Detection cooldown: {detectionCooldown} seconds");
                    SendPrivateMessage(username, "Use '!box help' for commands.");
                }
            }
        }
    }
}

// Helper classes to store player information
class PlayerInfo
{
    public DateTime EnteredTime { get; set; }
    public Location LastPosition { get; set; }
    public ArmorInfo ArmorInfo { get; set; }
    public bool EquipmentChecked { get; set; } = false;
}

class ArmorInfo
{
    public string Helmet { get; set; } = "None";
    public string Chestplate { get; set; } = "None";
    public string Leggings { get; set; } = "None";
    public string Boots { get; set; } = "None";
    public string MainHand { get; set; } = "None";
    public string OffHand { get; set; } = "None";
}
