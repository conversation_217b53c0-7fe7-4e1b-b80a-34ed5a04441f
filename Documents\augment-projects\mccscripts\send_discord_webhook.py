#!/usr/bin/env python3
"""
Script to send Discord webhook data from the file created by login_tracker.cs
Place this script in the same directory as your MCC client
"""

import os
import sys
import json
import time
import datetime
import requests
from pathlib import Path

# Discord webhook URL
WEBHOOK_URL = "https://discord.com/api/webhooks/1373055433538801714/G4HS8NED9mUYscI2LEJ4FJ-Ahs1pQuikIwi_x9p1XAxD2TIVkGQ32yqa5CDeJ-axqEWH"

# Path to the webhook data file
WEBHOOK_DATA_FILE = "discord_webhook_data.txt"

def send_webhook(webhook_url, payload):
    """Send webhook data to Discord"""
    try:
        headers = {
            "Content-Type": "application/json"
        }
        response = requests.post(webhook_url, data=payload, headers=headers)

        # Check if the request was successful
        if response.status_code == 204 or response.status_code == 200:
            print(f"✅ Discord webhook sent successfully! Status code: {response.status_code}")
            return True
        else:
            print(f"❌ Error sending Discord webhook. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Exception while sending webhook: {str(e)}")
        return False

def rename_file(file_path):
    """Rename the file to prevent sending it again"""
    try:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        new_file_path = f"{file_path}_{timestamp}"
        os.rename(file_path, new_file_path)
        print(f"✅ Webhook data file renamed to: {new_file_path}")
    except Exception as e:
        print(f"❌ Error renaming file: {str(e)}")

def create_join_payload(player_name, uuid, gif_url):
    """Create a Discord webhook payload for player join"""
    avatar_url = f"https://crafatar.com/avatars/{uuid}?size=128&overlay=true"
    timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()

    payload = {
        "username": "Minecraft Login Tracker",
        "embeds": [
            {
                "title": "Player Joined",
                "description": f"**{player_name}** has joined the server",
                "color": 0x00FF00,  # Green
                "timestamp": timestamp,
                "thumbnail": {
                    "url": avatar_url
                },
                "image": {
                    "url": gif_url
                }
            }
        ]
    }

    return json.dumps(payload)

def create_leave_payload(player_name, uuid, gif_url, session_duration_seconds):
    """Create a Discord webhook payload for player leave"""
    avatar_url = f"https://crafatar.com/avatars/{uuid}?size=128&overlay=true"
    timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()

    # Format session duration
    session_duration = float(session_duration_seconds)
    if session_duration >= 3600:  # 1 hour or more
        hours = int(session_duration // 3600)
        minutes = int((session_duration % 3600) // 60)
        seconds = int(session_duration % 60)
        duration_text = f"{hours}h {minutes}m {seconds}s"
    elif session_duration >= 60:  # 1 minute or more
        minutes = int(session_duration // 60)
        seconds = int(session_duration % 60)
        duration_text = f"{minutes}m {seconds}s"
    else:
        duration_text = f"{int(session_duration)}s"

    description = f"**{player_name}** has left the server"
    if session_duration > 0:
        description += f"\nSession duration: **{duration_text}**"

    payload = {
        "username": "Minecraft Login Tracker",
        "embeds": [
            {
                "title": "Player Left",
                "description": description,
                "color": 0xFF0000,  # Red
                "timestamp": timestamp,
                "thumbnail": {
                    "url": avatar_url
                },
                "image": {
                    "url": gif_url
                }
            }
        ]
    }

    return json.dumps(payload)

def process_webhook_data(webhook_data):
    """Process the webhook data and create a payload"""
    parts = webhook_data.strip().split('|')

    if len(parts) < 3:
        print(f"❌ Invalid webhook data format: {webhook_data}")
        return None

    event_type = parts[0]

    if event_type == "JOIN":
        if len(parts) >= 4:
            player_name = parts[1]
            uuid = parts[2]
            gif_url = parts[3]
            return create_join_payload(player_name, uuid, gif_url)
    elif event_type == "LEAVE":
        if len(parts) >= 5:
            player_name = parts[1]
            uuid = parts[2]
            gif_url = parts[3]
            session_duration = parts[4]
            return create_leave_payload(player_name, uuid, gif_url, session_duration)

    print(f"❌ Unknown event type or missing data: {event_type}")
    return None

def main():
    """Main function"""
    print(f"Discord Webhook Sender")
    print(f"=====================")

    # Check if the webhook data file exists
    if not os.path.exists(WEBHOOK_DATA_FILE):
        print(f"❌ Webhook data file not found: {WEBHOOK_DATA_FILE}")
        print(f"This file is created when a player joins or leaves the server.")
        return

    print(f"✅ Found webhook data file: {WEBHOOK_DATA_FILE}")

    # Read the webhook data file
    try:
        with open(WEBHOOK_DATA_FILE, 'r', encoding='utf-8') as file:
            webhook_data = file.read()

        # Check if the data is in JSON format
        try:
            # Try to parse as JSON first
            payload = json.loads(webhook_data)
            payload_json = webhook_data
            print("✅ Webhook data is in JSON format")
        except json.JSONDecodeError:
            # If not JSON, process as simple format
            print("ℹ️ Webhook data is in simple format, converting to JSON")
            payload_json = process_webhook_data(webhook_data)
            if not payload_json:
                print("❌ Failed to process webhook data")
                return

        # Send the webhook
        if send_webhook(WEBHOOK_URL, payload_json):
            # Rename the file to prevent sending it again
            rename_file(WEBHOOK_DATA_FILE)
    except Exception as e:
        print(f"❌ Error processing webhook data: {str(e)}")

def check_and_send_periodically(interval_seconds=10):
    """Check for webhook data files and send them periodically"""
    print(f"Starting periodic webhook sender (checking every {interval_seconds} seconds)")
    print(f"Press Ctrl+C to stop")

    try:
        while True:
            if os.path.exists(WEBHOOK_DATA_FILE):
                main()
            else:
                print(f"No webhook data file found. Waiting...")

            time.sleep(interval_seconds)
    except KeyboardInterrupt:
        print("\nStopping webhook sender")

if __name__ == "__main__":
    # Check if the script should run in periodic mode
    if len(sys.argv) > 1 and sys.argv[1] == "--periodic":
        interval = 10  # Default interval
        if len(sys.argv) > 2:
            try:
                interval = int(sys.argv[2])
            except ValueError:
                pass
        check_and_send_periodically(interval)
    else:
        main()
        input("Press Enter to exit...")
