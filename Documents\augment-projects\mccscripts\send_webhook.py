#!/usr/bin/env python3
"""
Discord Webhook Sender
This script reads webhook data from discord_webhook_data.txt and sends it to Discord
"""

import os
import json
import re
import requests
import random
from datetime import datetime

def clean_text(text):
    """Remove special characters and clean the text for JSON"""
    # Make a copy of the original for logging
    original = text

    # Dictionary of words that need to be corrected
    word_corrections = {
        'Swod': 'Sword',
        'Dak': 'Dark',
        'Daknesss': 'Darkness',
        'Gladia<PERSON>': 'Gladiator',
        'Favo': 'Favor',
        'State': 'Starter',
        'Fluy': '<PERSON>luffy',
        '<PERSON>luf': '<PERSON>luffy',
        '<PERSON>luff': '<PERSON>luffy'
    }

    # List of common words that should keep their 'r's
    common_words = [
        'Protection', 'Projectile', 'Fire', 'Armor', 'Respiration',
        '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'Feather', 'Sharpness', 'Arthropods',
        'Looting', 'Sweeping', 'Fortune', 'Power', 'Treasure', 'Piercing',
        'Curse', 'Breaker', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'Greenhouse',
        'Iron', 'Clarity', 'Refurbish', 'Unbreaking', 'Mending',
        'Warrior', '<PERSON>', 'Forged', 'Rest', '<PERSON>', 'Sell', 'Soul',
        'Inferno', 'Jolly', 'Crate', 'Exclusive', 'Dexterity',
        'Festive', 'Lifeforce', 'Blacksmith', 'Lungs', 'Planning',
        'Tesla', 'Coil', 'Fist', 'Revel', 'Anguish', 'Sword', 'Dark',
        'Darkness', 'Gladiator', 'Favor', 'Starter', 'Carrot',
        'Fluffy', 'Fluff', 'Furry', 'Fur'
    ]

    # Remove all Minecraft formatting codes and special characters
    text = re.sub(r'§[0-9a-fk-or]', '', text)  # Remove Minecraft formatting codes

    # Remove the § character itself (in case it's not followed by a valid formatting code)
    text = text.replace('§', '')

    # Remove special characters
    text = text.replace('������', '')  # Remove special characters
    text = text.replace('�', '')       # Remove replacement character
    text = text.replace('❃', '')       # Remove flower symbol
    text = text.replace('◆', '')       # Remove diamond symbol
    text = text.replace('[', '')       # Remove brackets
    text = text.replace(']', '')       # Remove brackets

    # Remove common problematic sequences
    text = text.replace('77', '')
    text = text.replace('ff', '')
    text = text.replace('rr', '')
    text = text.replace('88', '')
    text = text.replace('00', '')
    text = text.replace('66', '')
    text = text.replace('cc', '')

    # Remove any remaining non-ASCII characters
    text = re.sub(r'[^\x20-\x7E]', '', text)   # Remove non-printable ASCII

    # Split the text into words
    words = text.split()
    cleaned_words = []

    for word in words:
        # First check if this is a word that needs specific correction
        if word in word_corrections:
            cleaned_words.append(word_corrections[word])
            continue

        # Check for partial matches (for compound words)
        corrected = False
        for incorrect, correct in word_corrections.items():
            if incorrect in word and not corrected:
                word = word.replace(incorrect, correct)
                corrected = True

        # Instead of removing all 'r's, we'll only remove them in specific patterns
        # that are likely to be Minecraft formatting codes

        # Check if this might be a known word with 'r's
        matched_common_word = False
        for common_word in common_words:
            # If the word without 'r's matches the common word without 'r's
            if word.lower().replace('r', '') == common_word.lower().replace('r', ''):
                # Use the common word instead
                word = common_word
                matched_common_word = True
                break

        # Only apply 'r' removal if it's not a recognized common word
        if not matched_common_word:
            # Look for patterns like 'rFr', 'rLr', etc. which are likely formatting codes
            word = re.sub(r'r[A-Za-z]r', '', word)

            # Don't completely strip all 'r's as that breaks words like "Fluffy"

        cleaned_words.append(word)

    # Join the words back together
    text = ' '.join(cleaned_words)

    # Remove any double spaces that might have been created
    text = re.sub(r'\s+', ' ', text)

    # Log the cleaning process if there was a significant change
    if original != text:
        print(f"Cleaned text: '{original[:50]}...' -> '{text[:50]}...'")

    return text.strip()

def format_equipment(equipment_text):
    """Format equipment text into Discord blockquotes"""
    # Skip the "Player has equipment:" line
    if equipment_text.startswith("Player has equipment:"):
        equipment_text = equipment_text[len("Player has equipment:"):].strip()

    # Extract equipment entries using regex
    pattern = r'(Helmet|Chestplate|Leggings|Boots|Main Hand|Off Hand):\s*([^:]+?)(?=(?:Helmet|Chestplate|Leggings|Boots|Main Hand|Off Hand):|$)'
    matches = re.finditer(pattern, equipment_text, re.DOTALL)

    formatted_text = ""

    # Special case replacements
    special_replacements = {
        "VGlitch": "Glitch",
        "VNutted": "Nutted",
        "Telekinesis": "Telekinesis V",
        "Tesla Coil": "Tesla Coil V",
        "Rest": "Rest V",
        "Nutted": "Nutted in by Glitch",
        "Glitch": "Glitch legs"
    }

    # Common enchantments to filter out
    common_enchants = [
        "Blast Protection V", "Fire Protection V", "Mending I",
        "Projectile Protection V", "Protection V", "Unbreaking V",
        "Unbreaking", "Unbreaking VNutted", "Unbreaking VGlitch",
        "Blast Protection", "Fire Protection", "Mending",
        "Projectile Protection", "Protection", "V"
    ]

    # List of multi-word enchantments
    multi_word_enchants = [
        'Iron Lungs', 'Meal Planning', 'Stay Calm', 'Tesla Coil',
        'Jolly Crate Exclusive', 'Festive Dexterity', 'Snowball Barrage',
        'Blast Protection', 'Fire Protection', 'Projectile Protection',
        'Bane of Arthropods', 'Sweeping Edge', 'Silk Touch',
        'Luck of the Sea', 'Quick Charge', 'Swift Sneak',
        'Demon Ward', 'Demon Breaker', 'Bane Of The Dark',
        'Darkness Blessing', 'Darkness Favor', 'Dark Protection',
        'Insatiable Greed', 'Insatiable Knowledge', 'Arrow Storm',
        'Cold Heart', 'Revel In Anguish', 'Iron Fist', 'Fire Forged',
        'Sell Soul', 'Inferno Crate Exclusive', 'Jolly Helmet',
        'The Dragon Warrior', 'Kung Fu I', 'Kung Fu V', 'Kung Fu X', 'Kung Fu',
        'Inferno Leggings',
        'Fire Aspect', 'Nutted in by Glitch', 'Saced Crate Exclusive',
        'Sharpness X (+4)', 'Glitch legs', 'Well Fed', 'Judgement',
        'Telekinesis', 'Poweful Blows', 'Runic Obstuction', 'Alacity',
        'Leaden', 'Hangy', 'Shockwave', 'Decapitation', 'Bamboozle',
        'Festive Dexterity I', 'Bamboozle I', 'Bamboozle V', 'Bamboozle X', 'Bamboozle',
        'Revel In Anguish I',
        'Poweful Blows VI (+1)', 'Runic Obstuction VI (+1)',
        'Nutted in by Glitch', 'Glitch legs', 'Ancient Crate Exclusive',
        'Tiki Crate Exclusive', 'Cupid Crate Exclusive', 'Time Taveled',
        'Ras Wath III', 'Eye Of Hous I', 'Rain Dance V', 'Rejuvenating Wate I',
        'Daknesss Favor VI', 'Daknesss Blessing', 'Faithful Romance III',
        'Swindled Shads XV', 'Twilight Blade X', 'Bloodstained Gold IX',
        'Wise X', 'Well Fed V', 'Fiewok Footsteps III', 'Daknesss Favor XVII',
        'Iron Forged I', 'Sharpness VII (+1)', 'Clown Crate Exclusive',
        'Up Up and Away!', 'Cicus Cannon II', 'Clowning Aound I',
        'Gift Wrap I', 'Cold Heart III', 'Grinch Crate Exclusive',
        'Toxic Crate Exclusive', 'Crate Exclusive', 't Virus XX t',
        'Grinch Sword', 'Feeze Beath II', 'Poweful Blows V',
        'Runic Obstuction V', 'Alacity III', 'Leaden II', 'Slam V',
        'Shockwave V', 'Hangy V', 'Decapitation IX', 'Fire Aspect III',
        'Looting III'
    ]

    for match in matches:
        equipment_type = match.group(1)
        equipment_content = match.group(2).strip()

        if equipment_content and equipment_content != "None":
            # Clean up the content
            clean_content = clean_text(equipment_content)

            # Split the content into words
            words = clean_content.split()

            # Common enchantment prefixes to look for
            enchant_prefixes = [
                "Protection", "Fire", "Blast", "Projectile", "Thorns", "Respiration",
                "Aqua", "Depth", "Frost", "Soul", "Feather", "Sharpness", "Smite",
                "Bane", "Knockback", "Looting", "Sweeping", "Efficiency", "Silk",
                "Unbreaking", "Fortune", "Power", "Punch", "Flame", "Infinity",
                "Luck", "Lure", "Loyalty", "Impaling", "Riptide", "Channeling",
                "Multishot", "Quick", "Piercing", "Mending", "Curse", "Swift",
                "Demon", "Darkness", "Dark", "Insatiable", "Echolocation", "Lifeforce",
                "Refurbish", "Blacksmith", "Greenhouse", "Iron", "Meal", "Stay",
                "Clarity", "Tesla", "Snowball", "Arrow", "Cold", "Sell", "Parry"
            ]

            # Known item names to look for
            known_item_names = [
                'Jolly Helmet',
                'The Dragon Warrior',
                'Inferno Leggings',
                'TotemOfUndying',
                'Saced Dih',
                'The Stallions Mane',
                'Tiki Chestplate',
                'Godlike Leggings',
                'Cupid Sword',
                'Toxic Rat Helmet',
                'Toxic Rat Chestplate',
                'Toxic Rat Leggings',
                'Up Up and Away!',
                'Toxic Rat',
                'Grinch Sword',
                'Fluffy',
                'Fluffy Helmet',
                'Fluffy Chestplate',
                'Fluffy Leggings',
                'Fluffy Boots'
            ]

            # Try to identify the item name
            item_name = ""
            enchantments = []
            enchant_start_index = 0

            # First check for known item names
            for known_name in known_item_names:
                name_parts = known_name.split()
                if len(words) >= len(name_parts):
                    potential_match = " ".join(words[:len(name_parts)])
                    if potential_match.lower().replace('r', '') == known_name.lower().replace('r', ''):
                        item_name = known_name
                        enchant_start_index = len(name_parts)
                        break

            # If no known item name was found, use a more generic approach
            if not item_name:
                # Find the first word that looks like an enchantment
                enchant_start_index = len(words)
                for i, word in enumerate(words):
                    for prefix in enchant_prefixes:
                        if word.lower() == prefix.lower():
                            enchant_start_index = i
                            break
                    if i == enchant_start_index:
                        break

                # Extract item name
                if enchant_start_index > 0:
                    item_name = " ".join(words[:enchant_start_index])
                else:
                    item_name = words[0] if words else "Unknown"

            # Extract enchantments
            i = enchant_start_index
            while i < len(words):
                # Check if this is a common enchantment to filter out
                is_common = False
                for enchant in common_enchants:
                    enchant_parts = enchant.split()
                    if i + len(enchant_parts) <= len(words) and " ".join(words[i:i+len(enchant_parts)]) == enchant:
                        # Skip this common enchantment
                        i += len(enchant_parts)
                        is_common = True
                        break

                if not is_common:
                    # Check if this is a multi-word enchantment
                    found_multi_word = False
                    for multi_enchant in multi_word_enchants:
                        multi_parts = multi_enchant.split()
                        if i + len(multi_parts) <= len(words):
                            # Check if the words match the multi-word enchantment
                            potential_match = " ".join(words[i:i+len(multi_parts)])
                            # Compare without case sensitivity and ignoring 'r's
                            if potential_match.lower().replace('r', '') == multi_enchant.lower().replace('r', ''):
                                # Found a multi-word enchantment

                                # Extract the base enchantment name (without level)
                                base_enchant = multi_enchant
                                if ' ' in multi_enchant and any(c in multi_enchant.split()[-1] for c in 'IVXLCDM'):
                                    base_enchant = ' '.join(multi_enchant.split()[:-1])

                                # Check for Roman numerals in the raw text
                                roman_numerals = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X',
                                                 'XI', 'XII', 'XIII', 'XIV', 'XV', 'XVI', 'XVII', 'XVIII', 'XIX', 'XX']

                                # Look for the enchantment level in the raw text
                                found_level = False
                                for numeral in roman_numerals:
                                    # Check different patterns of how the level might appear in the raw text
                                    patterns = [
                                        f"{base_enchant} {numeral}",
                                        f"{base_enchant} r{numeral}r",
                                        f"{base_enchant} r{numeral}rr",
                                        f"{base_enchant}r r{numeral}r"
                                    ]

                                    # For enchantments with special characters
                                    base_with_r = ''.join([f"r{c}r" if c.isalpha() else c for c in base_enchant])
                                    patterns.extend([
                                        f"{base_with_r} r{numeral}r",
                                        f"{base_with_r} r{numeral}rr",
                                        f"{base_with_r}r r{numeral}r"
                                    ])

                                    for pattern in patterns:
                                        if pattern in equipment_content:
                                            enchantments.append(f"{base_enchant} {numeral}")
                                            found_level = True
                                            break

                                    if found_level:
                                        break

                                # If no level was found, use the original multi-word enchantment
                                if not found_level:
                                    enchantments.append(multi_enchant)

                                i += len(multi_parts)
                                found_multi_word = True
                                break

                    if found_multi_word:
                        continue

                    # Check if this is the start of a single-word enchantment
                    for prefix in enchant_prefixes:
                        if i < len(words) and words[i].lower().replace('r', '') == prefix.lower().replace('r', ''):
                            # Found an enchantment
                            enchant = prefix  # Use the correct spelling
                            i += 1

                            # Check for level in the raw text
                            roman_numerals = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X',
                                             'XI', 'XII', 'XIII', 'XIV', 'XV', 'XVI', 'XVII', 'XVIII', 'XIX', 'XX']

                            # First check if the next word is a Roman numeral
                            if i < len(words) and words[i] in roman_numerals:
                                enchant += " " + words[i]
                                i += 1
                            else:
                                # Look for the level in the raw text
                                found_level = False
                                for numeral in roman_numerals:
                                    # Check different patterns
                                    patterns = [
                                        f"{prefix} {numeral}",
                                        f"{prefix} r{numeral}r",
                                        f"{prefix} r{numeral}rr",
                                        f"{prefix}r r{numeral}r"
                                    ]

                                    # For enchantments with special characters
                                    prefix_with_r = ''.join([f"r{c}r" if c.isalpha() else c for c in prefix])
                                    patterns.extend([
                                        f"{prefix_with_r} r{numeral}r",
                                        f"{prefix_with_r} r{numeral}rr",
                                        f"{prefix_with_r}r r{numeral}r"
                                    ])

                                    for pattern in patterns:
                                        if pattern in equipment_content:
                                            enchant += f" {numeral}"
                                            found_level = True
                                            break

                                    if found_level:
                                        break

                            enchantments.append(enchant)
                            found_multi_word = True
                            break

                    if found_multi_word:
                        continue

                    # Not a recognized enchantment, check if it's a level
                    if i < len(words) and words[i] in ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII', 'XIII', 'XIV', 'XV', 'XVI', 'XVII', 'XVIII', 'XIX', 'XX']:
                        # It's a level, try to attach it to the previous word
                        if enchantments:
                            enchantments[-1] += " " + words[i]
                        i += 1
                    else:
                        # Just add it as a separate word
                        enchantments.append(words[i])
                        i += 1

            # Apply special replacements to enchantments
            for i in range(len(enchantments)):
                for old_text, new_text in special_replacements.items():
                    if enchantments[i] == old_text:
                        enchantments[i] = new_text

            # Format the equipment with item name and vertical enchantments using blockquotes
            formatted_text += f"**{equipment_type}**: {item_name}\n"
            if enchantments:
                # Only show first two and last enchantment
                if len(enchantments) > 0:
                    # First enchantment
                    first_enchant = enchantments[0].strip()
                    if first_enchant:
                        formatted_text += f"> {first_enchant}\n"

                    # Second enchantment (if available)
                    if len(enchantments) > 1:
                        second_enchant = enchantments[1].strip()
                        if second_enchant:
                            formatted_text += f"> {second_enchant}\n"

                    # Last enchantment (if different from first two)
                    if len(enchantments) > 2:
                        # Look for Crate Exclusive in any enchantment
                        last_enchant = None
                        for enchant in enchantments:
                            if "Crate Exclusive" in enchant:
                                last_enchant = enchant.strip()
                                break

                        # If no Crate Exclusive found, use the last enchantment
                        if not last_enchant:
                            last_enchant = enchantments[-1].strip()

                        # Only add if it's different from the first two
                        if last_enchant and last_enchant != first_enchant and (len(enchantments) <= 1 or last_enchant != second_enchant):
                            formatted_text += f"> {last_enchant}\n"

                formatted_text += "\n"
            else:
                formatted_text += "\n"

    if not formatted_text:
        formatted_text = "No equipment detected"

    return formatted_text

def has_netherite(equipment_text):
    """Check if the player has netherite equipment"""
    # Look for netherite-related keywords in the equipment text
    netherite_keywords = [
        'Netherite', 'Ancient', 'Godlike', 'Toxic', 'Tiki', 'Cupid',
        'Jolly', 'Dragon', 'Inferno', 'Stallions', 'Saced', 'NetheritePickaxe',
        'NetheriteLeggings', 'NetheriteChestplate', 'NetheriteHelmet', 'NetheriteBoots',
        'NetheriteSword', 'NetheriteAxe', 'NetheriteHoe', 'NetheriteShovel',
        'Jrorlrlryr', 'Trhre', 'Irnrfrerrrnror', 'Hrerlrmrertrr', 'Lrergrgrirnrgrsrr',
        'rTrorxrirc', 'Rat'
    ]

    # Keywords that indicate non-netherite equipment
    non_netherite_keywords = [
        'Starter', 'Diamond', 'DiamondSword', 'DiamondHelmet',
        'DiamondLeggings', 'DiamondChestplate', 'DiamondBoots',
        'Iron', 'Gold', 'Leather', 'Chain', 'Gladiator'
    ]

    # Debug output
    print(f"Checking for netherite in equipment text: {equipment_text[:100]}...")

    # Special case for Gladiator - always exclude it
    if 'gladiator' in equipment_text.lower() or 'grlrardrirartrorrr' in equipment_text.lower():
        print("Found Gladiator equipment - skipping webhook")
        return False

    # First check if this is non-netherite equipment
    for keyword in non_netherite_keywords:
        if keyword.lower() in equipment_text.lower() and 'netherite' not in equipment_text.lower():
            print(f"Found non-netherite keyword: {keyword}")
            # Check if there are any netherite keywords that would override this
            for netherite_keyword in netherite_keywords:
                if netherite_keyword.lower() in equipment_text.lower():
                    print(f"Found netherite keyword that overrides non-netherite: {netherite_keyword}")
                    return True
            return False

    # Check for each netherite keyword
    for keyword in netherite_keywords:
        if keyword.lower() in equipment_text.lower():
            print(f"Found netherite keyword: {keyword}")
            return True

    # If we didn't find any keywords, check for specific item types
    if "Slot 2 (Helmet): NetheritePickaxe" in equipment_text or \
       "Slot 65 (Leggings): NetheriteLeggings" in equipment_text or \
       "Slot 66 (Chestplate): NetheriteChestplate" in equipment_text:
        print("Found netherite item in equipment slots")
        return True

    # If no netherite keywords were found, don't send the webhook
    print("No netherite equipment found")
    return False

def main():
    """Main function to read data and send webhook"""
    webhook_data_file = "discord_webhook_data.txt"

    # List of GIFs to randomly choose from
    gif_urls = [
        "https://media.discordapp.net/attachments/941002586432094249/948830944381530142/image0.gif?ex=68297fad&is=68282e2d&hm=2f572683f41e578af1f1b4e33012465b075bde7217b194ad705487c56000cbf6&=",
        "https://media.discordapp.net/attachments/1274499669841412206/1306886832386084906/heist.gif?ex=6828e675&is=682794f5&hm=5f6a6c4931dd45960d3319069fb1a27a13603e61857d49af1307ae2589df783a&=",
        "https://media.discordapp.net/attachments/1175903778910769152/1197874462473855016/little_kid.gif?ex=68292447&is=6827d2c7&hm=57bba9b825b630d1e61acc0f32abed5b3528aa5d0053d6208f951446b14cc8c0&",
        "https://media.discordapp.net/attachments/1263184392101036032/1263594090432303258/output-onlinegiftools.gif?ex=6828f233&is=6827a0b3&hm=e1485f11f15d910e78c2535aece765157a62578fa6d3cab7100457ec47210129&=",
        "https://media.discordapp.net/attachments/1241420350647177287/1241541254794317876/ezgif-1-19d0cfeecd.gif?ex=68292365&is=6827d1e5&hm=39ec7c65a899709ff08a870db0341bc866b3d06c76594b27ff0d3af863b73d15&=",
        "https://media.discordapp.net/attachments/1310953039968079872/1317231439950909461/caption.gif?ex=6828f5e0&is=6827a460&hm=cb6c1534319c5b8fa1414281123ed5423fa353589d0e43f3e5f6d9342e0fd913&=",
        "https://media.discordapp.net/attachments/937013984035479594/1344182212765159457/fewarewillingtorape.gif?ex=682972bd&is=6828213d&hm=80b2ae56fc987827fcd6bebacc5045b262349107d6623f8310e0cc1a03805ec4&="
    ]

    if not os.path.exists(webhook_data_file):
        print(f"Webhook data file not found: {webhook_data_file}")
        return

    # Read the webhook data
    with open(webhook_data_file, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    # Parse the data
    webhook_url = None
    player_name = None
    formatted_equipment_lines = []

    print(f"Reading webhook data file with {len(lines)} lines")

    # First pass: extract webhook URL and player name
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('WEBHOOK_URL='):
            webhook_url = line[len('WEBHOOK_URL='):]
            print(f"Found webhook URL: {webhook_url[:20]}...")
        elif line.startswith('PLAYER_NAME='):
            player_name = line[len('PLAYER_NAME='):]
            print(f"Found player name: {player_name}")
        elif line.startswith('FORMATTED_EQUIPMENT='):
            # Read the formatted equipment (which may span multiple lines)
            formatted_equipment_lines.append(line[len('FORMATTED_EQUIPMENT='):])
            i += 1

            # Continue reading until the end of the file or a new key
            while i < len(lines) and not lines[i].strip().startswith('#') and '=' not in lines[i]:
                formatted_equipment_lines.append(lines[i].strip())
                i += 1

            # Adjust the index since we'll increment it at the end of the loop
            i -= 1

            print(f"Found formatted equipment ({len(formatted_equipment_lines)} lines)")

        i += 1

    if not webhook_url:
        print("No webhook URL found in the data file")
        return

    if not player_name:
        player_name = "Unknown Player"

    # Process the formatted equipment lines
    equipment_sections = []
    current_section = []

    for line in formatted_equipment_lines:
        # Clean the line
        cleaned_line = clean_text(line)

        # Skip empty lines
        if not cleaned_line:
            if current_section:
                equipment_sections.append(current_section)
                current_section = []
            continue

        # Add the line to the current section
        current_section.append(cleaned_line)

    # Add the last section if it's not empty
    if current_section:
        equipment_sections.append(current_section)

    # Format the equipment sections
    formatted_sections = []

    for section in equipment_sections:
        if not section:
            continue

        # Extract the title and item
        title_line = section[0]
        if '**' in title_line and ':' in title_line:
            # Extract the title (e.g., "Helmet")
            title = title_line.split('**')[1].split('**')[0]

            # Fix "O Hand" to "Off Hand"
            if title == "O Hand" or title == "O":
                title = "Off Hand"

            # Extract the item name (e.g., "Netherite Helmet")
            item_name = title_line.split(':')[1].strip()

            # Format the section with Markdown
            formatted_section = f"**{title}: {item_name}**"

            # Add the enchantments
            for i in range(1, len(section)):
                line = section[i]
                if line.startswith('>'):
                    # Keep the '>' prefix for blockquote formatting
                    enchant = line.strip()
                    formatted_section += f"\n{enchant}"

            formatted_sections.append(formatted_section)

    # Create the formatted description with Markdown
    description = f"## ⚠️ {player_name} HAS DROPPED INTO WARZONE ⚠️\n"
    description += "\n\n".join(formatted_sections)

    # Select a random GIF from the list
    random_gif_url = random.choice(gif_urls)

    # Create the embed
    embed = {
        "description": description,
        "color": 15158332,  # Red color
        "image": {
            "url": random_gif_url
        }
    }

    # Create the payload with just the embed (no role pinging)
    payload = {
        "embeds": [embed]
    }

    print("Sending webhook with payload:")
    print(json.dumps(payload, indent=2))

    # Send the webhook
    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 204:
            print("Discord webhook sent successfully!")
        else:
            print(f"Error sending webhook: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Exception sending webhook: {e}")

if __name__ == "__main__":
    main()
