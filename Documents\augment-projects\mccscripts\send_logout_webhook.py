import requests
import json
import random

# Read the webhook data from the file
webhook_data = {}
with open("logout_webhook_data.txt", "r") as file:
    for line in file:
        if "=" in line:
            key, value = line.strip().split("=", 1)
            webhook_data[key] = value

# Get the webhook URL and message
webhook_url = webhook_data.get("WEBHOOK_URL", "")
message = webhook_data.get("MESSAGE", "")
color = int(webhook_data.get("COLOR", "16711680"))  # Default to red
no_gif = webhook_data.get("NO_GIF", "false").lower() == "true"

# Create the embed
embed = {
    "title": "Player Logout",
    "description": message,
    "color": color
}

# Only add image if NO_GIF is not set to true
if not no_gif:
    # Random GIFs for logout messages
    logout_gifs = [
        "https://media.giphy.com/media/42D3CxaINsAFemFuId/giphy.gif",
        "https://media.giphy.com/media/3ohhwMULIutDIMNh28/giphy.gif",
        "https://media.giphy.com/media/l0HlBO7eyXzSZkJri/giphy.gif",
        "https://media.giphy.com/media/3o7TKEP6YngkCKFofC/giphy.gif",
        "https://media.giphy.com/media/26u4b45b8KlgAB7iM/giphy.gif"
    ]
    embed["image"] = {
        "url": random.choice(logout_gifs)
    }

# Create the payload
payload = {
    "embeds": [embed]
}

# Send the webhook
try:
    response = requests.post(
        webhook_url,
        data=json.dumps(payload),
        headers={"Content-Type": "application/json"}
    )
    response.raise_for_status()
    print(f"Webhook sent successfully! Status code: {response.status_code}")
except Exception as e:
    print(f"Error sending webhook: {e}")
