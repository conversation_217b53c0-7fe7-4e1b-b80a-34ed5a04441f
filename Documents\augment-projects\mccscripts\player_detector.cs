//MCCScript 1.0

MCC.LoadBot(new PlayerDetector());

//MCCScript Extensions

public class PlayerDetector : ChatBot
{
    // Define the monitored box area using the coordinates provided
    // X: -12 to 13
    // Y: 90 to 164
    // Z: -134 to -110
    private readonly Location areaMin = new Location(-12, 90, -134);
    private readonly Location areaMax = new Location(13, 164, -110);

    // How often to check for players (in milliseconds)
    private readonly int checkInterval = 1000;

    // Track when we last checked
    private DateTime lastCheckTime = DateTime.MinValue;

    // Track players who are in the area
    private Dictionary<string, DateTime> playersInArea = new Dictionary<string, DateTime>();

    // Track players on cooldown to avoid spam
    private Dictionary<string, DateTime> playerCooldowns = new Dictionary<string, DateTime>();

    // Cooldown period to avoid spam (in seconds)
    private int detectionCooldown = 10;

    public override void Initialize()
    {
        LogToConsole($"=== Player Detector Initialized ===");
        LogToConsole($"Monitoring box area with coordinates:");
        LogToConsole($"X: {areaMin.X} to {areaMax.X}");
        LogToConsole($"Y: {areaMin.Y} to {areaMax.Y}");
        LogToConsole($"Z: {areaMin.Z} to {areaMax.Z}");

        // Check if entity handling is enabled
        if (!GetTerrainEnabled())
        {
            LogToConsole("WARNING: Terrain handling is disabled!");
            LogToConsole("Player tracking may not work correctly.");
            LogToConsole("Please enable 'TerrainAndMovements=true' in MinecraftClient.ini");
        }

        if (!GetEntityHandlingEnabled())
        {
            LogToConsole("WARNING: Entity handling is disabled!");
            LogToConsole("Player tracking will not work correctly.");
            LogToConsole("Please enable 'EntityHandling=true' in MinecraftClient.ini");
        }
    }

    public override void Update()
    {
        // Only check at the specified interval
        if ((DateTime.Now - lastCheckTime).TotalMilliseconds < checkInterval)
            return;

        lastCheckTime = DateTime.Now;

        // Get all entities
        Dictionary<int, Entity> entities = GetEntities();

        // Log the number of entities
        LogToConsole($"Found {entities.Count} entities in the world");

        // Track which players are currently in the area
        HashSet<string> currentPlayersInArea = new HashSet<string>();

        // Get player position for reference
        Location playerPos = GetCurrentLocation();
        LogToConsole($"Player position: X:{playerPos.X:F1}, Y:{playerPos.Y:F1}, Z:{playerPos.Z:F1}");

        // Check each entity
        foreach (var entity in entities.Values)
        {
            // Only interested in players
            if (entity.Type == EntityType.Player && !string.IsNullOrEmpty(entity.Name))
            {
                // Skip if it's the client player - just log all players for now
                // We'll filter out our own player later if needed

                // Calculate distance to player
                double distance = Math.Sqrt(
                    Math.Pow(playerPos.X - entity.Location.X, 2) +
                    Math.Pow(playerPos.Y - entity.Location.Y, 2) +
                    Math.Pow(playerPos.Z - entity.Location.Z, 2)
                );

                // Log all players
                LogToConsole($"Player detected: {entity.Name} at X:{entity.Location.X:F1}, Y:{entity.Location.Y:F1}, Z:{entity.Location.Z:F1} ({distance:F1} blocks away)");

                // Check if player is in the monitored area
                if (IsLocationInArea(entity.Location))
                {
                    currentPlayersInArea.Add(entity.Name);

                    // Check if player is on cooldown
                    bool isOnCooldown = false;
                    if (playerCooldowns.ContainsKey(entity.Name))
                    {
                        TimeSpan timeSinceCooldown = DateTime.Now - playerCooldowns[entity.Name];
                        if (timeSinceCooldown.TotalSeconds < detectionCooldown)
                        {
                            isOnCooldown = true;
                        }
                        else
                        {
                            // Cooldown expired, remove from cooldown list
                            playerCooldowns.Remove(entity.Name);
                        }
                    }

                    // If this is a new player in the area and not on cooldown
                    if (!playersInArea.ContainsKey(entity.Name) && !isOnCooldown)
                    {
                        playersInArea[entity.Name] = DateTime.Now;
                        OnPlayerEnteredArea(entity.Name, entity.Location);

                        // Add player to cooldown list
                        playerCooldowns[entity.Name] = DateTime.Now;
                    }
                }
            }
        }

        // Check for players who left the area
        List<string> playersWhoLeft = new List<string>();
        foreach (var player in playersInArea)
        {
            if (!currentPlayersInArea.Contains(player.Key))
            {
                // Player is no longer in the area
                playersWhoLeft.Add(player.Key);
                OnPlayerLeftArea(player.Key, player.Value);
            }
        }

        // Remove players who left from tracking
        foreach (string player in playersWhoLeft)
        {
            playersInArea.Remove(player);
        }
    }

    private bool IsLocationInArea(Location loc)
    {
        return loc.X >= areaMin.X && loc.X <= areaMax.X &&
               loc.Y >= areaMin.Y && loc.Y <= areaMax.Y &&
               loc.Z >= areaMin.Z && loc.Z <= areaMax.Z;
    }

    private void OnPlayerEnteredArea(string playerName, Location location)
    {
        LogToConsole($"ALERT: Player {playerName} entered the box area!");
        LogToConsole($"Position: X:{location.X:F1}, Y:{location.Y:F1}, Z:{location.Z:F1}");

        // Play alert sound
        for (int i = 0; i < 3; i++)
        {
            Console.Beep(880, 200);
            Thread.Sleep(100);
        }
    }

    private void OnPlayerLeftArea(string playerName, DateTime enteredTime)
    {
        TimeSpan timeInArea = DateTime.Now - enteredTime;
        LogToConsole($"Player {playerName} left the box area after {timeInArea.TotalMinutes:F1} minutes");
    }
}
