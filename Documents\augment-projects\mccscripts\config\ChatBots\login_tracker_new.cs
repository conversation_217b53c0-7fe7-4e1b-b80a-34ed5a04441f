//MCCScript 1.0

MCC.LoadBot(new LoginTracker());

//MCCScript Extensions

using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

/// <summary>
/// This script tracks player logins and logouts and sends notifications to a Discord webhook.
/// </summary>
public class LoginTracker : ChatBot
{
    // Discord webhook URL
    private string webhookUrl = "https://discord.com/api/webhooks/1373055433538801714/G4HS8NED9mUYscI2LEJ4FJ-Ahs1pQuikIwi_x9p1XAxD2TIVkGQ32yqa5CDeJ-axqEWH";
    
    // Configuration options
    private bool logToFile = true;
    private string logFilePath = "login_tracker_log.txt";
    private string configFilePath = "login_tracker_config.json";
    
    // GIF URLs for join/leave messages
    private string[] joinGifUrls = new string[] {
        "https://media.giphy.com/media/Nx0rz3jtxtEre/giphy.gif",
        "https://media.giphy.com/media/l0MYC0LajbaPoEADu/giphy.gif",
        "https://media.giphy.com/media/ASd0Ukj0y3qMM/giphy.gif",
        "https://media.giphy.com/media/3o7TKUAOqDm3SQle92/giphy.gif",
        "https://media.giphy.com/media/l4JyOCNEfXvVYEqB2/giphy.gif"
    };
    
    private string[] leaveGifUrls = new string[] {
        "https://media.giphy.com/media/42D3CxaINsAFemFuId/giphy.gif",
        "https://media.giphy.com/media/3ohhwMULIutDIMNh28/giphy.gif",
        "https://media.giphy.com/media/l0HlBO7eyXzSZkJri/giphy.gif",
        "https://media.giphy.com/media/3o7TKEP6YngkCKFofC/giphy.gif",
        "https://media.giphy.com/media/26u4b45b8KlgAB7iM/giphy.gif"
    };
    
    // Random number generator for selecting GIFs
    private Random random = new Random();
    
    // Dictionary to track when players joined (to calculate session time)
    private Dictionary<string, DateTime> playerJoinTimes = new Dictionary<string, DateTime>();

    public override void Initialize()
    {
        // Load configuration
        LoadConfig();
        
        // Display initialization message
        LogToConsole("=== Login Tracker Initialized ===");
        LogToConsole($"Tracking player logins and logouts");
        LogToConsole($"Discord webhook: {(string.IsNullOrEmpty(webhookUrl) ? "Not set" : "Configured")}");
        
        // Initialize log file if enabled
        if (logToFile)
        {
            try
            {
                // Create or append to log file with header
                File.AppendAllText(logFilePath,
                    $"\n\n=== Login Tracker Session Started at {DateTime.Now} ===\n\n");
            }
            catch (Exception e)
            {
                LogToConsole($"Error creating log file: {e.Message}");
                logToFile = false;
            }
        }
    }
    
    private void LoadConfig()
    {
        try
        {
            // Check if config file exists
            if (File.Exists(configFilePath))
            {
                // Read the config file line by line
                string[] lines = File.ReadAllLines(configFilePath);
                
                foreach (string line in lines)
                {
                    // Skip comments and empty lines
                    if (string.IsNullOrWhiteSpace(line) || line.TrimStart().StartsWith("#"))
                        continue;
                    
                    // Split by = to get key-value pairs
                    int equalsPos = line.IndexOf('=');
                    if (equalsPos > 0)
                    {
                        string key = line.Substring(0, equalsPos).Trim();
                        string value = line.Substring(equalsPos + 1).Trim();
                        
                        // Apply settings based on key
                        switch (key.ToLower())
                        {
                            case "webhookurl":
                                webhookUrl = value;
                                break;
                                
                            case "logtofile":
                                bool.TryParse(value, out logToFile);
                                break;
                        }
                    }
                }
                
                LogToConsole("Configuration loaded from file.");
            }
            else
            {
                // Create default config file
                List<string> configLines = new List<string>
                {
                    "# Login Tracker Configuration",
                    "# Edit this file to configure the script",
                    "",
                    "# Discord webhook URL",
                    $"WebhookURL={webhookUrl}",
                    "",
                    "# Enable or disable logging to file (true/false)",
                    $"LogToFile={logToFile}"
                };
                
                File.WriteAllLines(configFilePath, configLines);
                
                LogToConsole("Default configuration file created.");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error loading configuration: {ex.Message}");
            LogToConsole("Using default settings.");
        }
    }
    
    public override void OnPlayerJoin(Guid uuid, string name)
    {
        // Record join time for calculating session duration later
        playerJoinTimes[name] = DateTime.Now;
        
        // Log the event
        string message = $"Player {name} joined the server at {DateTime.Now}";
        LogToConsole(message);
        
        // Log to file
        if (logToFile)
        {
            try
            {
                File.AppendAllText(logFilePath, $"[{DateTime.Now}] JOIN: {name} (UUID: {uuid})\n");
            }
            catch (Exception ex)
            {
                LogToConsole($"Error writing to log file: {ex.Message}");
            }
        }
        
        // Send to Discord webhook
        SendJoinWebhook(name, uuid.ToString());
    }
    
    public override void OnPlayerLeave(Guid uuid, string name)
    {
        // Calculate session duration if we have the join time
        TimeSpan sessionDuration = TimeSpan.Zero;
        string durationText = "";
        
        if (playerJoinTimes.ContainsKey(name))
        {
            sessionDuration = DateTime.Now - playerJoinTimes[name];
            durationText = $" after {FormatTimeSpan(sessionDuration)}";
            playerJoinTimes.Remove(name);
        }
        
        // Log the event
        string message = $"Player {name} left the server at {DateTime.Now}{durationText}";
        LogToConsole(message);
        
        // Log to file
        if (logToFile)
        {
            try
            {
                File.AppendAllText(logFilePath, 
                    $"[{DateTime.Now}] LEAVE: {name} (UUID: {uuid}){(sessionDuration > TimeSpan.Zero ? $", Session: {FormatTimeSpan(sessionDuration)}" : "")}\n");
            }
            catch (Exception ex)
            {
                LogToConsole($"Error writing to log file: {ex.Message}");
            }
        }
        
        // Send to Discord webhook
        SendLeaveWebhook(name, uuid.ToString(), sessionDuration);
    }
    
    private string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalHours >= 1)
        {
            return $"{(int)timeSpan.TotalHours}h {timeSpan.Minutes}m {timeSpan.Seconds}s";
        }
        else if (timeSpan.TotalMinutes >= 1)
        {
            return $"{(int)timeSpan.TotalMinutes}m {timeSpan.Seconds}s";
        }
        else
        {
            return $"{timeSpan.Seconds}s";
        }
    }
    
    private void SendJoinWebhook(string playerName, string uuid)
    {
        if (string.IsNullOrEmpty(webhookUrl))
        {
            LogToConsole("Discord webhook URL not set. Skipping webhook notification.");
            return;
        }
        
        try
        {
            // Get a random GIF URL
            string gifUrl = joinGifUrls[random.Next(joinGifUrls.Length)];
            
            // Create simple webhook data
            string webhookData = $"JOIN|{playerName}|{uuid}|{gifUrl}";
            
            // Write to file for external processing
            string webhookDataFile = "discord_webhook_data.txt";
            File.WriteAllText(webhookDataFile, webhookData);
            
            LogToConsole($"Join notification data written to {webhookDataFile}");
            LogToConsole($"Run the Python script to send this data to Discord");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error preparing webhook data: {ex.Message}");
        }
    }
    
    private void SendLeaveWebhook(string playerName, string uuid, TimeSpan sessionDuration)
    {
        if (string.IsNullOrEmpty(webhookUrl))
        {
            LogToConsole("Discord webhook URL not set. Skipping webhook notification.");
            return;
        }
        
        try
        {
            // Get a random GIF URL
            string gifUrl = leaveGifUrls[random.Next(leaveGifUrls.Length)];
            
            // Create simple webhook data
            string webhookData = $"LEAVE|{playerName}|{uuid}|{gifUrl}|{sessionDuration.TotalSeconds}";
            
            // Write to file for external processing
            string webhookDataFile = "discord_webhook_data.txt";
            File.WriteAllText(webhookDataFile, webhookData);
            
            LogToConsole($"Leave notification data written to {webhookDataFile}");
            LogToConsole($"Run the Python script to send this data to Discord");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error preparing webhook data: {ex.Message}");
        }
    }
}
