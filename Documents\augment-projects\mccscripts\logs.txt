[MCC] [Script] Starting compilation for .\wz.cs...
[MCC] [Script] Compilation failed with error(s):
[MCC] [Script] Error in .\wz.cs, on line (using System;): [CS1001] Identifier expected
[MCC] [Script] Error in .\wz.cs, on line (using System.Collections.Generic;): [CS1001] Identifier expected
[MCC] [Script] Error in .\wz.cs, on line (using System.IO;): [CS1001] Identifier expected
[MCC] [Script] Error in .\wz.cs, on line (using System.Threading;): [CS1001] Identifier expected
[MCC] [Script] Error in .\wz.cs, on line (using Newtonsoft.Json;): [CS1001] Identifier expected
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Scripting;): [CS1001] Identifier expected
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Mapping;): [CS1001] Identifier expected
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Mapping;): [CS1513] } expected
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Mapping;): [CS1513] } expected
[MCC] [Script] Error in .\wz.cs, on line (}}): [CS1022] Type or namespace definition, or end-of-file expected
[MCC] [Script] Error in .\wz.cs, on line (}}): [CS1022] Type or namespace definition, or end-of-file expected
[MCC] [Script] Error in .\wz.cs, on line (using MCCScript;): [CS0246] The type or namespace name 'MCCScript' could not be found (are you missing a using directive or an assembly reference?)
[MCC] [Script] Error in .\wz.cs, on line (public class Script {): [CS0101] The namespace 'ScriptLoader' already contains a definition for 'Script'
[MCC] [Script] Error in .\wz.cs, on line (using System;): [CS0118] 'System' is a namespace but is used like a type
[MCC] [Script] Error in .\wz.cs, on line (using System;): [CS0210] You must provide an initializer in a fixed or using statement declaration
[MCC] [Script] Error in .\wz.cs, on line (using System.Collections.Generic;): [CS0118] 'System.Collections.Generic' is a namespace but is used like a type
[MCC] [Script] Error in .\wz.cs, on line (using System.Collections.Generic;): [CS0210] You must provide an initializer in a fixed or using statement declaration
[MCC] [Script] Error in .\wz.cs, on line (using System.IO;): [CS0118] 'System.IO' is a namespace but is used like a type
[MCC] [Script] Error in .\wz.cs, on line (using System.IO;): [CS0210] You must provide an initializer in a fixed or using statement declaration
[MCC] [Script] Error in .\wz.cs, on line (using System.Threading;): [CS0118] 'System.Threading' is a namespace but is used like a type
[MCC] [Script] Error in .\wz.cs, on line (using System.Threading;): [CS0210] You must provide an initializer in a fixed or using statement declaration
[MCC] [Script] Error in .\wz.cs, on line (using Newtonsoft.Json;): [CS0118] 'Newtonsoft.Json' is a namespace but is used like a type
[MCC] [Script] Error in .\wz.cs, on line (using Newtonsoft.Json;): [CS0210] You must provide an initializer in a fixed or using statement declaration
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Scripting;): [CS0118] 'MinecraftClient.Scripting' is a namespace but is used like a type
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Scripting;): [CS0210] You must provide an initializer in a fixed or using statement declaration
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Mapping;): [CS0118] 'MinecraftClient.Mapping' is a namespace but is used like a type
[MCC] [Script] Error in .\wz.cs, on line (using MinecraftClient.Mapping;): [CS0210] You must provide an initializer in a fixed or using statement declaration
[MCC] [Script] Error in .\wz.cs, on line (public object __run(CSharpAPI __apiHandler, string[] args) {): [CS0161] 'Script.__run(CSharpAPI, string[])': not all code paths return a value
[MCC] [Script] Error in .\wz.cs, on line (dynamic config = JsonConvert.DeserializeObject(json);): [CS0103] The name 'JsonConvert' does not exist in the current context
[MCC] [Script] Error in .\wz.cs, on line (string json = JsonConvert.SerializeObject(config, Formatting.Indented);): [CS0103] The name 'JsonConvert' does not exist in the current context
[MCC] [Script] Error in .\wz.cs, on line (string json = JsonConvert.SerializeObject(config, Formatting.Indented);): [CS0103] The name 'Formatting' does not exist in the current context
[MCC] [Script] Error in .\wz.cs, on line (RegisterChatBotTimer(ScanForPlayers, 1000);): [CS0103] The name 'RegisterChatBotTimer' does not exist in the current context
[MCC] [Script] Error in .\wz.cs, on line (Dictionary<int, Entity> entities = world.Entities;): [CS1061] 'World' does not contain a definition for 'Entities' and no accessible extension method 'Entities' accepting a first argument of type 'World' could be found (are you missing a using directive or an assembly reference?)
[MCC] [Script] Script '.\wz.cs' failed to run (InvalidScript).
[MCC] [Script] System.InvalidProgramException: Compilation failed due to error(s).