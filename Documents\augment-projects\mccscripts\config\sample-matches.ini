# Minecraft Console Client
# AutoRespond matches
# Example config file

# Structure of a match: [Match] Followed by the match and action
# The match can be a simple match or an advanced regular expression
# You can use $u for username of the player triggering the match
# You can define an action if the match was in a private message
# You can define an action if the match was not sent by a player
# Regex matches are also supported eg $1, $2, $3.. in actions
# Matches can optionally be restricted to bot owners only
# Matches can have a cooldown, specified in seconds

# When running a script from an AutoRespond match,
# additional %variables% are available from within your script:
# %match_u% will hold username of the player triggering the match
# %match_1%, %match_2%, %match_3%.. will hold regex matches

# Simple example: Respond to a message containing a keyword

[Match]
match=hi
action=send hi, $u!
actionprivate=send /tell $u Hello!
actionother=log detected "hi" message
ownersonly=false
cooldown=0

# You do not need to specify all the "action" fields
# Only one of them is required for each match

# Advanced example: Use a regular expression
# Here a "regex" field is used instead of "match" field
# Do not use both "regex" and "match" fields...

[Match]
regex=^.*hello ([a-zA-Z0-9_]+).*$
action=send hello too, $1!

# Example of using a script

[Match]
match=dotest
action=script test

# Example of matching a server announcement

[Match]
match=server is restarting
actionother=script restart

# Example of custom remote control command

[Match]
match=gohome
actionprivate=send /home
ownersonly=true

# Example of match with 1-minute cooldown

[Match]
match=hello
action=send hello!
cooldown=60

# Example of matching colored text when "matchcolors" is set to "true"
# §a stands for green text
# List of all color codes: https://minecraft.tools/en/color-code.php
[Match]
match=§ahello
action=send Hello back!

# Enjoy!
# - ORelio
